<template>
	<view class="mobile-item-container">
		<!-- <Navbar title="到货确认" bgColor="#fff" :h5Show="false"></Navbar> -->
		<view class="header">
			<u-search :showAction="true" actionText="搜索" placeholder="请输入收货编码" :animation="true"
				shape="square"></u-search>
			<u-subsection :list="subList" mode="subsection" :current="current" @change="sectionChange"></u-subsection>
		</view>
		<view class="content">
			<uni-card v-for="item in purchaseList" :key="item.id" :title="item.purchaseNo" @click="handleItem(item)">

			
				<view v-show="item.lineState=='CREATED'" class="createdState">
					{{getLineStateText(item.lineState)}}  
				</view>
				<view v-show="item.lineState=='EXECUTING'" class="executingState">
					{{getLineStateText(item.lineState)}}  
				</view>
				<view v-show="item.lineState=='FINISHED'" class="defauleState">
					{{getLineStateText(item.lineState)}}  
				</view>
				<view>
					<span>供应商1：</span><text>{{ item.supplierName }}</text>
				</view>
				<view>
					<span>创建时间：</span><text>{{ item.createTime }}</text>
				</view>
			</uni-card>
		</view>
	</view>
</template>

<script>
	import * as purchaseApi from "@/api/work/purchase/purchase.js";
	import * as dictApi from "@/api/dict/dict.js";
	import Navbar from "@/components/navbar/Navbar";

	export default {
		components: {
			Navbar,
		},
		data() {
			return {
				subList: ["待办", "已办"],
				current: 0,
				// 收货单表格数据
				purchaseList: [],
				// 请求参数
				queryParams: {
					pageNum: 1,
					pageSize: 10,
				},
				// 字典数据
				lineState: [],
			};
		},
		onLoad() {
			this.queryParams.lineStateArr = ["CREATED", "EXECUTING"];
			this.initData();
			// 监听自定义事件
			uni.$on('refreshPurchaseList', () => {
				this.getList();
			        });
		},
		onUnload() {
		        // 页面卸载时移除事件监听
		        uni.$off('refreshPurchaseList');
		    },
		methods: {
			// 初始化数据
			async initData() {
				try {
					await this.getDicts();
					await this.getList();
				} catch (error) {
					console.error("初始化数据失败:", error);
				}
			},
			// 获取字典数据
			async getDicts() {
				try {
					const res = await dictApi.getDicts("line_state_dict");
					this.lineState = res.data;
				} catch (error) {
					console.error("获取字典数据失败:", error);
					uni.showToast({
						icon: "none",
						title: "获取字典数据失败",
					});
				}
			},
			// 获取列表数据
			async getList() {
				try {
					uni.showLoading({
						title: "加载中...",
					});
					const result = await purchaseApi.listPurchase(this.queryParams);
					if (result.code === 200) {
						this.purchaseList = result.rows;
						// uni.showToast({
						// 	icon: "success",
						// 	title: result.msg,
						// });
					} else {
						uni.showToast({
							icon: "none",
							title: result.msg || "获取数据失败",
						});
					}
				} catch (error) {
					console.error("获取列表数据失败:", error);
					uni.showToast({
						icon: "none",
						title: "获取列表数据失败",
					});
				} finally {
					uni.hideLoading();
				}
			},
			// 根据状态值获取对应的文本
			getLineStateText(state) {
				const stateItem = this.lineState.find((item) => item.dictValue === state);
				
				return stateItem ? stateItem.dictLabel : "未知状态";
			},
			// 切换子标签
			sectionChange(index) {
			
				this.current = index;
				if (index == 0) {
					this.queryParams.lineStateArr = ["CREATED", "EXECUTING"];
				} else {
					this.queryParams.lineStateArr = ["FINISHED"];
				}
				this.getList();

				// 可根据需求添加切换后的逻辑，比如重新加载数据
			},
			// 点击查看详情
			handleItem(item) {
				uni.navigateTo({
					url: "/pages/subPack/wms/confirmation/detail?params=" +
						encodeURIComponent(JSON.stringify(item)),
				});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.mobile-item-container {
		padding: 8px;
	}

	.notice-item-tips {
		border-radius: 8px;
		background-color: #f4f4f5;
		padding: 0px 10px !important;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		align-items: center;
	}

	::v-deep .u-subsection {
		margin-top: 10rpx;
	}

	::v-deep .uni-card {
		margin: 10rpx 0 !important;
		padding: 0 !important;

		.uni-card__header-extra {
			padding: 10rpx;
			background-color: yellow;
			border-radius: 5px;
			color: #000 !important;
		}
	}
</style>