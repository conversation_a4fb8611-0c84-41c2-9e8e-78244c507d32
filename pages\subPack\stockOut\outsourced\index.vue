<template>
	<view class="mobile-item-container">
		<view class="header">
			<u-search v-model="queryParams.stockOutNo" :showAction="true" actionText="搜索" placeholder="请输入入库单号"
				:animation="true" shape="square" @search="handleSearch" @custom="handleSearch" />
			<u-subsection :list="subList" mode="subsection" :current="currentTab" @change="handleTabChange" />
		</view>

		<view class="content">
			<uni-card v-for="item in stockOutList" :key="item.id" :title="item.stockOutNo"
				:extra="getStatusText(item.stockOutState)" @click="handleItemClick(item)" :class="'status-' + item.stockOutState">
				<view class="card-content-item">
					<span>供&ensp;应&ensp;商：</span>
					<text>{{ item.supplierName || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>入库日期：</span>
					<text>{{ item.stockOutDate || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>创建时间：</span>
					<text>{{ item.createTime || '--' }}</text>
				</view>
			</uni-card>

			<u-empty v-if="!loading && stockOutList.length === 0" mode="list" />
		</view>

		<u-loadmore v-if="stockOutList.length > 0" :status="loadStatus" />
	</view>
</template>

<script>
	
	import {
		outSourList
	} from '@/api/work/stockOut/outsource'
	import {
		getDicts
	} from '@/api/dict/dict'

	export default {
		data() {
			return {
				subList: ['待办', '已办'],
				currentTab: 0,
				stockOutList: [],
				statusDict: [],
				loading: false,
				loadStatus: 'loadmore',
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					StockOutType: 'OUTSOURC',
					StockOutStateArr: ["STOCK_OUT_PENDING","PUT_OUT_STORAGE"],
          // QualityState: 'INSPECTED', // Add state filter when needed
          // stockInStateArr: ['STOCK_PENDING','PUT_STORAGE']
				},
				total: 0
			}
		},

		async created() {
			await this.loadInitialData()
		},

		onReachBottom() {
			if (this.queryParams.pageNum * this.queryParams.pageSize < this.total) {
				this.queryParams.pageNum++
				this.loadData()
			}
		},

		methods: {
			async loadInitialData() {
				try {
					this.loading = true
					await Promise.all([this.loadDictData(), this.loadData()])
				} catch (error) {
					this.handleError(error, '初始化数据失败')
				} finally {
					this.loading = false
				}
			},

			async loadDictData() {
				try {
					const res = await getDicts('stock_out_state')
					this.statusDict = res.data || []
				} catch (error) {
					this.handleError(error, '获取字典数据失败')
				}
			},

			async loadData() {
				try {
					this.loadStatus = 'loading'
					const res = await outSourList(this.queryParams)

					if (res.code === 200) {
						this.total = res.total
						if (this.queryParams.pageNum === 1) {
							this.stockOutList = res.rows || []
						} else {
							this.stockOutList = [...this.stockOutList, ...(res.rows || [])]
						}

						this.loadStatus = res.rows.length < this.queryParams.pageSize ? 'nomore' : 'loadmore'
					} else {
						this.showToast(res.msg || '获取数据失败', 'none')
					}
				} catch (error) {
					this.handleError(error, '获取列表数据失败')
				}
			},

			handleSearch() {
				this.queryParams.pageNum = 1
				this.loadData()
			},

			handleTabChange(index) {
				this.currentTab = index
				this.queryParams = {
					...this.queryParams,
					pageNum: 1,
					stockOutStateArr: index === 0 ? ['STOCK_OUT_PENDING','PUT_OUT_STORAGE'] :
						['BE_OUT_STORAGE'] // Replace with actual state values
				}
				this.loadData()
			},

			getStatusText(state) {
				const statusItem = this.statusDict.find(item => item.dictValue === state)
				return statusItem?.dictLabel || '未知状态'
				// console.log("state=============>",state)
				// return statusItem?.dictValue || '未知状态'
			},

			handleItemClick(item) {
				console.log(item, 'item');
				uni.navigateTo({
					url: `/pages/subPack/stockOut/outsourced/detail?params=${encodeURIComponent(JSON.stringify(item))}`
				})
			},

			formatTime(time) {
				return time ? formatDate(time, 'YYYY-MM-DD HH:mm:ss') : ''
			},

			handleError(error, message) {
				console.error(message, error)
				this.showToast(message, 'none')
			},

			showToast(message, icon = 'none') {
				uni.showToast({
					title: message,
					icon
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.mobile-item-container {
		padding: 16rpx;
		min-height: 100vh;
		background-color: #f5f5f5;

		.header {
			background-color: #fff;
			padding: 20rpx;
			margin-bottom: 20rpx;
			border-radius: 8rpx;

			::v-deep .u-subsection {
				margin-top: 20rpx;
			}
		}

		.content {
			::v-deep .uni-card {
				margin: 20rpx 0 !important;
				border-radius: 12rpx;

				.uni-card__header-extra {
					padding: 8rpx 16rpx;
					// background-color: #ffcc00;
					border-radius: 8rpx;
					color: #333 !important;
					font-size: 24rpx;
				}
			}

			.card-content-item {
				margin-bottom: 12rpx;
				font-size: 28rpx;
				color: #666;

				span {
					color: #999;
				}

				&:last-child {
					margin-bottom: 0;
				}
			}
		}
	}
</style>