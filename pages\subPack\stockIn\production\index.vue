<template>
	<view class="mobile-item-container">
		<!-- 头部区域：包含搜索框和分段器 -->
		<view class="header">
			<!-- u-search组件用于搜索入库单，支持输入框和搜索按钮 -->
			<u-search v-model="queryParams.stockInNo" :showAction="true" actionText="搜索" placeholder="请输入入库单号"
				:animation="true" shape="square" @search="handleSearch" @custom="handleSearch" />
			<!-- u-subsection组件用于切换待办/已办选项卡 -->
			<u-subsection :list="subList" mode="subsection" :current="currentTab" @change="handleTabChange" />
		</view>

		<!-- 内容区域：显示入库单列表 -->
		<view class="content">
			<!-- 使用v-for循环渲染入库单卡片 -->
			<uni-card v-for="item in procureList" :key="item.id" :title="item.stockInNo"
				:extra="getStatusText(item.stockInState)" @click="handleItemClick(item)" :class="'status-' + item.stockInState">
				<!-- 卡片内容：显示入库单详细信息 -->
				<view class="card-content-item">
					<span>仓库编码：</span>
					<text>{{ item.warehouseCode || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>仓库名称：</span>
					<text>{{ item.warehouseName || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>入库日期：</span>
					<text>{{ item.stockInDate || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>创建时间：</span>
					<text>{{ item.createTime || '--' }}</text>
				</view>
			</uni-card>

			<!-- 数据为空时显示空状态组件 -->
			<u-empty v-if="!loading && procureList.length === 0" mode="list" />
		</view>

		<!-- 加载更多组件，根据加载状态显示不同提示 -->
		<u-loadmore v-if="procureList.length > 0" :status="loadStatus" />
	</view>
</template>
<!-- YUXK -->
<script>
	// 导入生产入库单API和数据字典API
	import {
		listProduction
	} from '@/api/work/stockIn/production'
	import {
		getDicts
	} from '@/api/dict/dict'
	// import { formatDate } from '@/utils/date'

	export default {
		data() {
			return {
				subList: ['待办', '已办'], // 分段器选项
				currentTab: 0, // 当前选中的选项卡索引
				procureList: [], // 入库单列表数据
				statusDict: [], // 状态字典数据
				loading: false, // 全局加载状态
				loadStatus: 'loadmore', // 加载更多状态：loading/loadmore/nomore
				queryParams: {
					pageNum: 1, // 当前页码
					pageSize: 10, // 每页数量
					StockInType: 'PRODUCE', // 入库类型：生产入库
					stockInStateArr: ['STOCK_PENDING', 'PUT_STORAGE'] // 待办状态列表
				},
				total: 0 // 数据总条数
			}
		},

		// 页面初始化时加载初始数据
		async created() {
			await this.loadInitialData();
			// 监听自定义事件消息
			uni.$on('refreshStockInList', this.loadInitialData); 
		},

		// 页面触底事件，用于加载更多数据
		onReachBottom() {
			if (this.queryParams.pageNum * this.queryParams.pageSize < this.total) {
				this.queryParams.pageNum++
				this.loadData()
			}
		},

		methods: {
			// 页面销毁时移除事件监听
			beforeDestroy() {
				uni.$off('refreshStockInList');
			},
			// 加载初始数据（字典数据和列表数据）
			async loadInitialData() {
				try {
					this.loading = true
					// 并行加载字典数据和列表数据
					await Promise.all([this.loadDictData(), this.loadData()])
				} catch (error) {
					this.handleError(error, '初始化数据失败')
				} finally {
					this.loading = false
				}
			},

			// 加载状态字典数据
			async loadDictData() {
				try {
					const res = await getDicts('stock_in_state')
					this.statusDict = res.data || []
				} catch (error) {
					this.handleError(error, '获取字典数据失败')
				}
			},

			// 加载入库单列表数据
			async loadData() {
				try {
					this.loadStatus = 'loading'
					// 调用API获取列表数据
					const res = await listProduction(this.queryParams)

					if (res.code === 200) {
						this.total = res.total
						if (this.queryParams.pageNum === 1) {
							// 第一页数据直接赋值
							this.procureList = res.rows || []
						} else {
							// 非第一页数据追加到现有数据后面
							this.procureList = [...this.procureList, ...(res.rows || [])]
						}
						console.log("获取生产入库列表了", res.rows);
						// 根据返回数据量设置加载更多状态
						this.loadStatus = res.rows.length < this.queryParams.pageSize ? 'nomore' : 'loadmore'
					} else {
						this.showToast(res.msg || '获取数据失败', 'none')
					}
				} catch (error) {
					this.handleError(error, '获取列表数据失败')
				}
			},

			// 处理搜索事件
			handleSearch() {
				this.queryParams.pageNum = 1 // 重置页码为1
				this.loadData() // 重新加载数据
			},

			// 处理选项卡切换事件
			handleTabChange(index) {
				this.currentTab = index
				// 根据选项卡索引更新查询参数中的状态列表
				this.queryParams = {
					...this.queryParams,
					pageNum: 1, // 重置页码为1
					stockInStateArr: index === 0 ? ['STOCK_PENDING', 'PUT_STORAGE'] : ['BE_IN_STORAGE']
				}
				this.loadData() // 重新加载数据
			},

			// 根据状态值获取状态文本
			getStatusText(state) {
				// 从字典数据中查找对应状态文本
				const statusItem = this.statusDict.find(item => item.dictValue === state)
				return statusItem?.dictLabel || '未知状态'
			},

			// 处理卡片点击事件（导航到详情页）
			handleItemClick(item) {
				uni.navigateTo({
				  url: `/pages/subPack/stockIn/production/detail?params=${encodeURIComponent(JSON.stringify(item))}`
				})
			},

			// 格式化时间（当前未使用）
			formatTime(time) {
				return time ? formatDate(time, 'YYYY-MM-DD HH:mm:ss') : ''
			},

			// 错误处理方法
			handleError(error, message) {
				console.error(message, error)
				this.showToast(message, 'none')
			},

			// 显示提示框
			showToast(message, icon = 'none') {
				uni.showToast({
					title: message,
					icon
				})
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>