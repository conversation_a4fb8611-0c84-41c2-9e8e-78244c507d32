<template>
	<view class="arrcal">
		<view class="header">
			<u-search :showAction="true" actionText="搜索" :animation="true" shape="square"></u-search>
		</view>
		<u-collapse>
			<!-- 采购单信息 -->
			<u-collapse-item title="采购单信息">
				<u-icon name="tags-fill" size="20" slot="icon"></u-icon>
				<uni-card>
					<view>
						<span>供应商编号：</span><text>{{ objData.supplierCode }}</text>
					</view>
					<view>
						<span>采购单号：</span><text>{{ objData.purchaseNo }}</text>
					</view>
					<view>
						<span>创建时间：</span><text>{{ objData.createTime }}</text>
					</view>
				</uni-card>
			</u-collapse-item>
			<!-- 采购单明细 -->
			<u-collapse-item title="采购单明细">
				<text slot="right-icon" class="u-page__item__title__slot-title">自定义内容</text>
				<scroll-view scroll-y="auto">
					<uni-card v-for="(item,index) in detailList" :key="item.id" :title="'序号 ' + item.purchaseLine"
						:extra="getLineStateText(item.lineState)">
						<view class="cont">
							<view class="item-tj">
								<span>物料编号：<text>{{ item.partCode }}</text></span>
							</view>
							<view class="item-tj">
								<span>物料名称：<text>{{ item.partName }}</text></span>
							</view>
							<view class="item-tj">
								<span>规格型号：<text>{{ item.materialName }}</text></span>
							</view>
							<view class="item-tj item-tb">
								<span>单位：<text>{{ item.materialName }}</text></span>
							</view>
							<view class="item-tj item-tb">
								<span>应收数量：<text>{{ item.qty }}</text></span>
								<span>已收：<text>{{ item.materialName }}</text></span>
								<span>本次收：<text>{{ item.materialName }}</text></span>
							</view>
						</view>
						<view class="man">
							<view class="man_header" @click="handClick(index)">
								<span class="title">箱标签</span>
								<span class="icon"><u-icon name="arrow-down" size="20"></u-icon></span>
							</view>
							<view class="" v-show="visibleBoxIndex === index">
								<view class="box" v-for="i in 5" :key="i.id">
									<view class="">
										<span>箱号：<text>{{ i.hao }}</text></span>
									</view>
									<view class="">
										<span>数量：<text>{{ i.shu }}</text></span>
									</view>
									<view class="btn">
										<u-button type="error" size="mini">删除</u-button>
									</view>
								</view>
							</view>
						</view>
					</uni-card>
				</scroll-view>
				
			</u-collapse-item>
		</u-collapse>
		<view class="btns">
			<u-button type="error" size="small">提交</u-button>
		</view>
	</view>
</template>

<script>
	import * as purchaseApi from '@/api/work/purchase/purchase.js'
	import * as dictApi from '@/api/dict/dict.js'
	export default {
		data() {
			return {
				objData: {}, // 页面传递的数据
				detailList: [], // 明细数据
				// 请求参数
				queryParams: {
					pageNum: 1,
					pageSize: 10,
				},
				// 字典数据
				lineState: [],
				boxList: [], //箱标签数据
				boxParams: {
					pageNum: 1,
					pageSize: 10,
				},
				show:true,
				visibleBoxIndex: 0, // 当前显示的箱标签索引
			};
		},
		onLoad(option) {
			try {
				const item = JSON.parse(decodeURIComponent(option.params));
				if (item) {
					this.objData = item;
					this.getPurDetaiList()
				}
			} catch (error) {
				console.error('解析参数失败:', error);
				uni.showToast({
					icon: 'none',
					title: '参数解析失败'
				});
			} finally {
				this.getDict()
			}
		},
		methods: {
			// 获取字典数据
			async getDict() {
				const res = await dictApi.getDicts('line_state_dict');
				this.lineState = res.data;
			},
			// 获取详细数据
			async getPurDetaiList() {
				try {
					uni.showLoading({
						title: '加载中...'
					});
					this.queryParams.purchaseNo = this.objData.id
					const result = await purchaseApi.purchaseDetail(this.queryParams);
					if (result.code === 200) {
						this.detailList = result.rows;
						uni.showToast({
							icon: 'success',
							title: result.msg
						});
					} else {
						uni.showToast({
							icon: 'none',
							title: result.msg || '获取数据失败'
						});
					}
				} catch (error) {
					console.error('获取列表数据失败:', error);
					uni.showToast({
						icon: 'none',
						title: '获取列表数据失败'
					});
				} finally {
					uni.hideLoading();
				}
			},
			// 根据状态值获取对应的文本
			getLineStateText(state) {
				const stateItem = this.lineState.find(item => item.dictValue === state);
				return stateItem ? stateItem.dictLabel : '未知状态';
			},
			// 获取箱标列表
			async getBox() {
				this.boxParams.receiveDetailId = this.objData.id
				const res = await purchaseApi.receiveBox()
			},
			handClick(index) {
				this.visibleBoxIndex = this.visibleBoxIndex === index ? null : index;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.arrcal {
		height: calc(100vh - 100rpx);
		padding-bottom: 100rpx;
		.cont{
			padding: 0 10px;
		}
		.man {
			.man_header {
				display: flex;
				padding: 5px 10px;
				justify-content: space-between;
				border-top: 1px solid #ccc;
				border-bottom: 1px solid #ccc;
				.title {
					font-size: 15px;
					font-weight: 600;
				}
			}

			.box {
				border: 1px solid aquamarine;
				padding: 5px 10px;
				border-radius: 4px;
				margin: 8rpx 0;
				position: relative;
			}

			.btn {
				width: 30px;
				position: absolute;
				bottom: 8px;
				right: 40px;
			}
		}
		.btns{
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 100rpx;
			::v-deep .u-button{
				height: 100%;
				font-size: 15px !important;
			}
		}
	}

	::v-deep .uni-card {
		margin: 10rpx 0 !important;
		padding: 0 !important;

		.uni-card__header-extra {
			padding: 10rpx;
			background-color: #fffae6;
			/* 修改为更柔和的黄色 */
			border-radius: 5px;
			color: #000 !important;
		}

		.item-tj {
			span {
				display: inline-block;
				width: 100%;
			}
		}

		.item-tb {
			span {
				display: inline-block;
				width: 33%;
			}
		}
	}

	::v-deep .u-collapse {
		height: 100% !important;

		.u-collapse-item__content {
			// height: auto !important;
		}

		.u-collapse-item__content__text {
			padding: 0 8px !important;

			// height: 100% !important;
			.u-cell__title-text {
				color: #000 !important;
				font-weight: 600;
			}
		}
	}
</style>