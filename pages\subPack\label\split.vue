<template>
	<view class="container">
		<!-- 搜索栏 -->
		<u-sticky>
			<view class="search-bar">
				<u-search label="箱号" placeholder="请扫码或者输入箱标签" searchIcon="scan" @clickIcon="handleScan"
					@search="handleSearch" @custom="handleSearch" shape="square" clearable
					v-model="searchValue"></u-search>
			</view>
		</u-sticky>

		<!-- 卡片内容 -->
		<view class="card-container" v-show="boxInfo.id">
			<uni-card :title="`标签号 ${boxInfo.boxNo}`">
				<!-- 物料信息 -->
				<view class="material-info">
					<view class="info-row">
						<text class="label">物料名称：</text>
						<text class="value">{{boxInfo.materialName}}</text>
					</view>
					<view class="info-row">
						<text class="label">物料编号：</text>
						<text class="value">{{boxInfo.materialCode}}</text>
					</view>
					<view class="info-row">
						<text class="label">供 应 商：</text>
						<text class="value">{{boxInfo.supplierName}}</text>
					</view>
					<view class="info-row dual-column">
						<view class="info-item">
							<text class="label">批    次：</text>
							<text class="value">{{boxInfo.batchNo}}</text>
						</view>
						<view class="info-item">
							<text class="label">数    量：</text>
							<text class="value">{{boxInfo.qty}}</text>
						</view>
					</view>
				</view>

				<!-- 拆分操作区 -->
				<view class="split-control">
					<view class="split-input">
						<text class="label">拆分张数：</text>
						<u-number-box v-model="splitCount" :min="1" :max="10" integer :disabled="!boxInfo.id">
							<view slot="minus" class="btn-minus">
								<u-icon name="minus" size="12"></u-icon>
							</view>
							<input slot="input" class="input-number" v-model="splitCount" type="number" :min="1"
								:max="10" />
							<view slot="plus" class="btn-plus">
								<u-icon name="plus" color="#FFFFFF" size="12"></u-icon>
							</view>
						</u-number-box>
					</view>
					<u-button type="primary" size="mini" @click="handleGenerate"
						:disabled="splitCount <= 0 || !boxInfo.id" :loading="generating">生成</u-button>
				</view>

				<!-- 生成的箱列表 -->
				<view class="box-list" v-if="boxList.length > 0">
					<view class="box-item" v-for="(box, index) in boxList" :key="box.id">
						<view class="box-index">{{ index + 1 }}</view>
						<view class="box-content">
							<view class="box-row">
								<text class="label">箱号：</text>
								<text class="value">{{ box.boxNo }}</text>
							</view>
							<view class="box-row quantity-control">
								<text class="label">数量：</text>
								<u-number-box v-model="box.qty" :min="0.001" :max="boxInfo.qty" :step="0.01"
									:precision="3" @change="(val) => handleQuantityChange(index, val)">
									<view slot="minus" class="btn-minus">
										<u-icon name="minus" size="12"></u-icon>
									</view>
									<input slot="input" class="input-number"  :value="box.qty" @input="(e) => handleInputChange(index, e)" type="number" />
									<view slot="plus" class="btn-plus">
										<u-icon name="plus" color="#FFFFFF" size="12"></u-icon>
									</view>
								</u-number-box>
							</view>
						</view>
					</view>
					<view class="total-qty">
						总数量：{{ totalQty.toFixed(2) }} / {{ boxInfo.qty }}
						<text v-if="Math.abs(totalQty - boxInfo.qty) > 0.001" class="qty-warning">(数量不匹配)</text>
					</view>
				</view>
			</uni-card>
		</view>
		<view class="btn">
			<u-button type="primary" throttleTime="1000" size="small" @click="firstSubmit">提 交</u-button>
		</view>
	</view>
</template>

<script>
	import * as purchaseApi from '@/api/work/purchase/purchase.js'

	export default {
		data() {
			return {
				searchValue: '',
				splitCount: 1,
				boxList: [],
				boxInfo: {},
				generating: false
			}
		},
		computed: {
			totalQty() {
				return this.boxList.reduce((sum, box) => sum + Number(box.qty), 0)
			}
		},
		methods: {
			handleScan() {
				uni.scanCode({
					success: (res) => {
						this.searchValue = res.result
						this.handleSearch(res.result)
					},
					fail: (err) => {
						uni.showToast({
							title: '扫码失败',
							icon: 'none'
						})
					}
				})
			},
			// 处理输入框变化
			handleInputChange(index, e) {
				
				 // 从事件对象中获取输入值
				  const value = e.detail.value;
				  
				console.log("输入框中的数量值为："+value),
				// 确保赋值安全
				this.$set(this.boxList, index, {
					...this.boxList[index],
					qty: Number(value)
				})
			},
			async handleSearch(value) {
				if (!value) return

				try {
					uni.showLoading({
						title: '查询中...'
					})
					const result = await purchaseApi.scanSplitCode({
						qrCode: value
					})

					if (result.code === 200) {
						this.boxInfo = result.data
						this.boxList = []
						this.splitCount = 1
						uni.showToast({
							icon: 'success',
							title: '查询成功'
						})
					} else {
						throw new Error(result.msg || '查询失败')
					}
				} catch (err) {
					uni.showToast({
						icon: 'none',
						title: err.msg || '查询异常'
					})
					this.boxInfo = {}
					this.boxList = []
				} finally {
					uni.hideLoading()
				}
			},

			async handleGenerate() {
				if (this.splitCount <= 0 || !this.boxInfo.id) return
				// 判断 splitCount 是否为有效数字
				if (isNaN(this.splitCount)) {
					// 提示用户输入有效数字
					uni.showToast({
						icon: 'none',
						title: '请输入有效数字!'
					})
					return;
				}
				this.generating = true
				try {
					// 先尝试调用API生成
					const params = {
						...this.boxInfo,
						printQty: this.splitCount
					}

					const result = await purchaseApi.autoCode(params)

					// if (result.code === 200 && result.data?.length) {
					//   // 使用API返回的数据
					//   this.boxList = result.data.map((item, index) => ({
					//     id: item.id || Date.now() + index,
					//     code: item.code || `箱号${index + 1}`,
					//     qty: item.qty || 0
					//   }))
					// } else {
					//   // API无数据时本地计算
					//   this.calculateSplitBoxes()
					// }
				} catch (err) {
					// console.error('生成失败:', err)
					// API失败时本地计算
					this.calculateSplitBoxes(err)
				} finally {
					this.generating = false
				}
			},

			calculateSplitBoxes(err) {
				const total = Number(this.boxInfo.qty)
				const count = err.length
				const average = total / count
				const boxes = []

				// 计算每个箱子的数量（保留3位小数）
				let remaining = total
				for (let i = 0; i < count; i++) {
					let qty
					if (i === count - 1) {
						qty = parseFloat(remaining.toFixed(3))
					} else {
						qty = parseFloat(average.toFixed(3))
						remaining -= qty
					}
					boxes.push({
						batchNo: this.boxInfo.batchNo,
						boxNo: err[i],
						boxState: this.boxInfo.boxState,
						comId: this.boxInfo.comId,
						createBy: this.boxInfo.createBy,
						createTime: this.boxInfo.createTime,
						dateCode: this.boxInfo.dateCode,
						daterangeA: this.boxInfo.daterangeA,
						delFlag: this.boxInfo.delFlag,
						expirationDate: this.boxInfo.expirationDate,
						materialCode: this.boxInfo.materialCode,
						materialId: this.boxInfo.materialId,
						materialName: this.boxInfo.materialName,
						materialUnit: this.boxInfo.materialUnit,
						orderClass: this.boxInfo.orderClass,
						parentBoxId: this.boxInfo.parentBoxId,
						parentBoxNo: this.boxInfo.parentBoxNo,
						printQty: this.boxInfo.printQty,
						purchaseNo: this.boxInfo.purchaseNo,
						purchaseNo: this.boxInfo.purchaseNo,
						qrCode: err[i],
						qty: qty,
						specification: this.boxInfo.specification,
						supplierCode: this.boxInfo.supplierCode,
						supplierId: this.boxInfo.supplierId,
						supplierName: this.boxInfo.supplierName,
						updateBy: this.boxInfo.updateBy,
						updateTime: this.boxInfo.updateTime,
					})
				}
				this.boxList = boxes
			},

			handleQuantityChange(index, newQty) {
				console.log(newQty, 'newQty');
				newQty = parseFloat(Number(newQty.value).toFixed(3))

				// 计算剩余数量需要重新分配的量
				const remainingTotal = this.boxInfo.qty - newQty
				const remainingBoxes = this.boxList.length - 1

				if (remainingBoxes <= 0) return
				console.log(this.boxInfo.qty, newQty, 'remainingTotal');
				// 重新计算其他箱子的数量
				const newAverage = remainingTotal / remainingBoxes
				let remaining = remainingTotal

				this.boxList.forEach((box, i) => {
					if (i !== index) {
						let qty
						if (i === this.boxList.length - 1) {
							qty = parseFloat(remaining.toFixed(3))
						} else {
							qty = parseFloat(newAverage.toFixed(3))
							remaining -= qty
						}
						box.qty = qty
					}
				})
			},
			// 提交
			async firstSubmit() {
				console.log(this.boxList.length == 0, 'this.boxList.');
				if (this.boxList.length == 0) return
				console.log('222');
				// 遍历 boxList 检查每个 box 的 qty 是否为有效数字
				for (let box of this.boxList) {
					console.log("本次qty为：" + box.qty);
					if (isNaN(Number(box.qty))) {
						// 提示用户输入有效数字
						uni.showToast({
							icon: 'none',
							title: '请输入有效数字!'
						});
						return;
					}
				}
				// 计算拆分后的总数量
				const totalSplitQty = this.boxList.reduce((sum, box) => sum + Number(box.qty), 0);

				// 检查拆分后的总数量是否超过原有箱标签中的数量
				if (totalSplitQty > this.boxInfo.qty) {
					console.log("拆分后的总数量为：" + totalSplitQty);
					console.log("原有数量为：" + this.boxInfo.qty);
					uni.showToast({
						icon: 'none',
						title: '拆分后总数量已超过原有数量'
					});
					return;
				}
				try {
					uni.showLoading({
						title: '提交中...'
					})
					const params = {
						id: this.boxInfo.id,
						boxNo: this.boxInfo.boxNo,
						state: this.boxInfo.boxState,
						detail: this.boxList,
						// qr_code
					}

					const result = await purchaseApi.splitSubmits(params)
					console.log(result, 'result');
					if (result.code === 200) {
						uni.showToast({
							icon: 'success',
							title: result.msg,
						})
						setTimeout(() => {
							uni.navigateBack()
						}, 1000)
					}
				} catch (err) {

				} finally {
					uni.hideLoading()
				}

			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative;

		.btn {
			width: 100%;
			background-color: #fff;
			height: 100rpx;
			position: fixed;
			bottom: 0;
			left: 0;
			padding: 20rpx 0;
			display: flex;
			align-items: center;
			justify-content: center;

			::v-deep .u-button {
				height: 80rpx;
				width: 90%;
				margin: 0;
				font-size: 28rpx
			}
		}
	}

	.search-bar {
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 8rpx;
		padding: 10rpx;
	}

	.card-container {
		::v-deep .uni-card {
			margin: 10rpx 0 !important;
			border-radius: 12rpx;

			.uni-card__header-extra {
				padding: 8rpx 16rpx;
				background-color: #fffae6;
				border-radius: 8rpx;
				color: #333;
				font-size: 24rpx;
			}
		}
	}

	.material-info {
		padding: 10rpx 0;

		.info-row {
			margin-bottom: 16rpx;
			display: flex;
			align-items: flex-start;

			&.dual-column {
				display: flex;
				justify-content: space-between;

				.info-item {
					// flex: 0 0 48%;
					display: flex;
				}
			}

			.label {
				color: #666;
				margin-right: 8rpx;
				white-space: nowrap;
				flex-shrink: 0;
			}

			.value {
				color: #333;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}

	.split-control {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 20rpx 0;
		padding: 16rpx 0;
		border-top: 1rpx solid #eee;
		border-bottom: 1rpx solid #eee;

		.split-input {
			display: flex;
			align-items: center;

			.label {
				margin-right: 16rpx;
				white-space: nowrap;
			}
		}

		::v-deep .u-button {
			min-width: 120rpx;
			margin: 0;
			width: 30%;
		}
	}

	.box-list {
		margin-top: 20rpx;

		.box-item {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;

			.box-index {
				width: 44rpx;
				height: 44rpx;
				border-radius: 50%;
				border: 1rpx solid #eee;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 16rpx;
				flex-shrink: 0;
				font-size: 24rpx;
			}

			.box-content {
				flex: 1;
				border: 1rpx solid #b3e5fc;
				border-radius: 8rpx;
				padding: 16rpx;
				background-color: #f7fbff;

				.box-row {
					display: flex;
					align-items: center;
					margin-bottom: 8rpx;

					&.quantity-control {
						justify-content: space-between;

						.label {
							margin-right: 20rpx;
						}
					}

					.label {
						color: #666;
						margin-right: 8rpx;
						white-space: nowrap;
					}
				}
			}
		}

		.total-qty {
			text-align: right;
			margin-top: 10rpx;
			font-size: 24rpx;
			color: #666;

			.qty-warning {
				color: #fa3534;
				margin-left: 10rpx;
			}
		}
	}

	/* 数字输入框样式 */
	.btn-minus {
		width: 44rpx;
		height: 44rpx;
		border: 1rpx solid #e6e6e6;
		border-radius: 50%;
		@include flex;
		justify-content: center;
		align-items: center;
	}

	.btn-plus {
		width: 44rpx;
		height: 44rpx;
		background-color: #3c9cff;
		border-radius: 50%;
		@include flex;
		justify-content: center;
		align-items: center;
	}

	.input-number {
		padding: 0 20rpx;
		text-align: center;
		font-size: 28rpx;
		width: 35px;
	}
</style>