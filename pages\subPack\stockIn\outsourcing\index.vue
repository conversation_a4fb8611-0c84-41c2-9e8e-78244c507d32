<template>
	<view class="mobile-item-container">
		<view class="header">
			<u-search v-model="queryParams.stockInNo" :showAction="true" actionText="搜索" placeholder="请输入入库单号"
				:animation="true" shape="square" @search="handleSearch" @custom="handleSearch" />
			<u-subsection :list="subList" mode="subsection" :current="currentTab" @change="handleTabChange" />
		</view>

		<view class="content">
			<uni-card v-for="item in outSouringList" :key="item.id" :title="item.stockInNo"
				:extra="getStatusText(item.stockInState)" @click="handleItemClick(item)" :class="'status-' + item.stockInState">
				<view class="card-content-item">
					<span>供&ensp;应&ensp;商：</span>
					<text>{{ item.supplierName || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>仓库编码：</span>
					<text>{{ item.warehouseCode || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>仓库名称：</span>
					<text>{{ item.warehouseName || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>入库日期：</span>
					<text>{{ item.stockInDate || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>创建时间：</span>
					<text>{{ item.createTime || '--' }}</text>
				</view>
			</uni-card>

			<u-empty v-if="!loading && outSouringList.length === 0" mode="list" />
		</view>

		<u-loadmore v-if="outSouringList.length > 0" :status="loadStatus" />
	</view>
</template>

<script>
	import {
		outsouList
	} from '@/api/work/stockIn/outsourcing.js'
	import {
		getDicts
	} from '@/api/dict/dict'
	// import { formatDate } from '@/utils/date'

	export default {
		data() {
			return {
				subList: ['待办', '已办'],
				currentTab: 0,
				outSouringList: [],
				statusDict: [],
				loading: false,
				loadStatus: 'loadmore',
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					StockInType:'ISSUE',
					stockInStateArr: ['STOCK_PENDING','PUT_STORAGE']
					// stockInNo: '',
					// stockInState: undefined // Add state filter when needed
				},
				total: 0
			}
		},

		async created() {
			await this.loadInitialData()
		},

		onReachBottom() {
			if (this.queryParams.pageNum * this.queryParams.pageSize < this.total) {
				this.queryParams.pageNum++
				this.loadData()
			}
		},

		methods: {
			async loadInitialData() {
				try {
					this.loading = true
					await Promise.all([this.loadDictData(), this.loadData()])
				} catch (error) {
					this.handleError(error, '初始化数据失败')
				} finally {
					this.loading = false
				}
			},

			async loadDictData() {
				try {
					const res = await getDicts('stock_in_state')
					this.statusDict = res.data || []
				} catch (error) {
					this.handleError(error, '获取字典数据失败')
				}
			},

			async loadData() {
				try {
					this.loadStatus = 'loading'
					const res = await outsouList(this.queryParams)					
					if (res.code === 200) {
						this.total = res.total
						if (this.queryParams.pageNum === 1) {
							this.outSouringList = res.rows || []
						} else {
							this.outSouringList = [...this.outSouringList, ...(res.rows || [])]
						}

						this.loadStatus = res.rows.length < this.queryParams.pageSize ? 'nomore' : 'loadmore'
					} else {
						this.showToast(res.msg || '获取数据失败', 'none')
					}
				} catch (error) {
					this.handleError(error, '获取列表数据失败')
				}
			},

			handleSearch() {
				this.queryParams.pageNum = 1
				this.loadData()
			},

			handleTabChange(index) {
				this.currentTab = index
				this.queryParams = {
					...this.queryParams,
					pageNum: 1,
					stockInStateArr: index === 0 ? ['STOCK_PENDING','PUT_STORAGE'] :
						['BE_IN_STORAGE','HAVE_BEEN','RETURNED','CLOSED'] // Replace with actual state values
				}
				this.loadData()
			},

			getStatusText(state) {
				const statusItem = this.statusDict.find(item => item.dictValue === state)
				return statusItem?.dictLabel || '未知状态'
			},

			handleItemClick(item) {
				console.log('222');
				uni.navigateTo({
				  url: `/pages/subPack/stockIn/outsourcing/detail?params=${encodeURIComponent(JSON.stringify(item))}`
				})
			},

			formatTime(time) {
				return time ? formatDate(time, 'YYYY-MM-DD HH:mm:ss') : ''
			},

			handleError(error, message) {
				console.error(message, error)
				this.showToast(message, 'none')
			},

			showToast(message, icon = 'none') {
				uni.showToast({
					title: message,
					icon
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
</style>