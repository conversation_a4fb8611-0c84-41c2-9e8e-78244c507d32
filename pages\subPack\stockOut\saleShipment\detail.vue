<template>
	<view class="procurement-detail">
		<!-- 固定搜索栏 -->
		<u-sticky>
			<view class="search-header" v-if="!['BE_OUT_STORAGE', 'TO_BE_CANCELLATION', 'IN_STOCK', 'ISSTOP','ALLRETURN'].includes(objData.stockOutState)">
				<!-- 栈板/箱号搜索 -->
				<view class="search-item">
					<u-search v-model="queryParams.containerNo" placeholder="请输入或扫描栈板/箱号" :showAction="true"
						actionText="搜索" shape="square" searchIcon="scan" @clickIcon="scanContainer"
						@search="handleContainerSearch" @custom="handleContainerSearch">
						<template #label>
							<view class="search-label">栈板/箱号</view>
						</template>
					</u-search>
				</view>
			</view>
		</u-sticky>

		<!-- 主内容区 -->
		<view class="content-container">
			<!-- 单据信息卡片 -->
			<view class="document-card">
				<view class="document-row">
					<text class="label">单据编号：</text>
					<text class="value">{{objData.stockOutNo}}</text>
				</view>
				<view class="document-row">
					<text class="label">出库类型：</text>
					<text class="value">{{getItemType(objData.stockOutType)}}</text>
				</view>
				<!-- <view class="document-row">
					<text class="label">供应商：</text>
					<text class="value">71000146-MM_UT01供应商</text>
				</view> -->
			</view>

			<!-- 扫描统计 -->
			<view class="scan-summary" v-if="!['BE_OUT_STORAGE', 'TO_BE_CANCELLATION', 'IN_STOCK', 'ISSTOP','ALLRETURN'].includes(objData.stockOutState)">
				<text class="label">实扫箱数：</text>
				<text class="value">{{count}}</text>
			</view>

			<u-divider />

			<!-- 物料明细卡片 @scrolltolower="loadMoreDetails" -->
			<view class="detail-list">
				<u-list v-if="detailList.length > 0" height="calc(100vh - 400rpx)" >
					<u-list-item v-for="(item, index) in detailList" :key="item.id">
						<view class="detail-card">
							<view class="card-header">
								<text class="title">序号 {{ index + 1 }}</text>
								<text class="status" :class="'status-' + item.stockOutState">{{ getLineStateText(item.stockOutState) }}</text>
							</view>


							<view class="card-content">
								<view class="content-row">
									<text class="label">物料编号：</text>
									<text class="value">{{ item.materialCode || '--' }}</text>
								</view>
								<view class="content-row">
									<text class="label">物料名称：</text>
									<text class="value">{{ item.materialName || '--' }}</text>
								</view>
								<view class="content-row">
									<text class="label">规格型号：</text>
									<text class="value">{{ item.specification || '--' }}</text>
								</view>
								<view class="content-row double">
									<view class="col">
										<text class="label">数量：</text>
										<text class="value">{{ item.qty || '--' }}</text>
									</view>
									<view class="col">
										<text class="label">单位：</text>
										<text class="value">{{ item.materialUnit || '--' }}</text>
									</view>
								</view>
								<view class="content-row triple">
									<view class="col">
										<text class="label">本次出库：</text>
										<text class="value">{{ item.nowOutQty || 0 }}</text>
									</view>
									<view class="col">
										<text class="label">已出库：</text>
										<text class="value">{{ item.incomingQty || 0 }}</text>
									</view>
								</view>
					<!-- 			<view class="content-row triple">
									<view class="col">
										<text class="label">合格数量：</text>
										<text class="value">{{ item.qualifiedQty || 0 }}</text>
									</view>
								</view> -->
							</view>

							<!-- 箱标签 -->
							<view class="box-tags" v-if="item.boxList.length >0">
								<view class="box-header" @click="toggleBoxList(index)">
									<text class="title">箱标签</text>
									<u-icon :name="visibleBoxIndex === index ? 'arrow-up' : 'arrow-down'"
										size="20"></u-icon>
								</view>

								<view class="box-list" v-show="visibleBoxIndex === index">
									<view class="box-item" v-for="(box, boxIndex) in item.boxList" :key="box.id">
										<view class="box-info">
											<text class="label">箱号：</text>
											<text class="value">{{ box.boxNo || '--' }}</text>
										</view>
										<view class="box-info">
											<text class="label">数量：</text>
											<text class="value">{{ box.qty || 0 }}</text>
										</view>
										<view class="box-info">
											<text class="label">库位：</text>
											<text class="value">{{ box.locationName || '--' }}</text>
										</view>
										<view class="box-action">
											<u-button type="error" size="mini"
												@click="handleDeleteBox(box.id, index, boxIndex)">删除</u-button>
										</view>
									</view>
								</view>
							</view>
						</view>
					</u-list-item>
				</u-list>

				<u-empty v-else mode="list" />
			</view>
		</view>	
	<!-- 	<view class="btns">
		
		</view> -->
		<!-- <view class="btns">
			<u-button type="success" size="small" @click="stockUp">备货</u-button>
			<u-button type="primary" size="small" :throttleTime="1000" @tap="firstSubmit">提交</u-button>
		</view> -->
		<view class="btns">
		 <!-- 修改备货按钮的显示条件 -->
		   <view class="btn-container" v-if="showStockButton">
		     <u-button type="success" size="small" @click="stockUp">备货</u-button>
		   </view>
		  <view class="btn-container">
		    <u-button type="primary" size="small" :throttleTime="1000" @tap="firstSubmit">提交</u-button>
		  </view>
		</view>
	</view>
</template>

<script>
	import * as produceApi from '@/api/work/stockOut/produce.js'
	import {
		getDicts
	} from '@/api/dict/dict'
	import {
		stockUpApi
	} from '@/api/work/stockOut/sale'
	export default {
		data() {
			return {
				loading: false,
				visibleBoxIndex: null, // 当前展开的箱标签索引
				detailList: [],
				objData:{},
				queryParams: {},
				detailLoadStatus: 'loadmore',
				outState:[],
				typeDicts:[],
				locationData:[],
				count:0,
				showStockButton: true, // 控制备货按钮的显示
			}
		},
		  
		async onLoad(options) {
			try {
				const item = JSON.parse(decodeURIComponent(options.params))
				if(item) {
					this.objData = item
					await Promise.all([
						this.getDict(),
						this.getProcureDetail()
					])
					console.log("item-------->",item)
				}
				if (result.code === 200) {
					this.locationData = result.data
					this.getProcureDetail()
				} else {
					this.$u.toast(result.msg || '获取数据失败');
				}
			} catch (error) {
	console.log('初始化失败:', error)
				// this.$u.toast('参数解析失败')
			}
		},

		methods: {
			// 获取字典数据
			async getDict() {
				try {
					const res = await getDicts('stock_out_state')
					this.outState = res.data || []
					const result = await getDicts('stock_out_type')
					this.typeDicts = result.data || []
				} catch (error) {
					// console.error('获取字典失败:', error)
					this.$u.toast('获取字典数据失败')
				}
			},
			// 获取采购单明细
			async getProcureDetail() {
				try {
					uni.showLoading({
						title: '加载中...'
					})
					
					const result = await produceApi.getDetailsAndBox(this.objData.id)
					console.log("result-------->",result)
					if (result.code === 200) {
						const rows = result.data.res || []
						this.count = result.data.stockOutBoxCount;
						// 初始化明细数据，添加boxList字段
						this.detailList = rows.map(item => ({
							...item,
							boxList: item.wmsStockOutBoxList,
							boxLoadStatus: 'loadmore',
							boxPageNum: 1
						}))
					
				// 		const jsonString = JSON.stringify(result.rows, null, 2);
				// 		console.log("result.rows"+jsonString)
				
				// 		// 使用示例
				// 		const total = calculateTotalQty(result);
				// 		console.log('Total Quantity:', total);
					} else {
						this.$u.toast(result.msg || '获取数据失败')
					}
				} catch (error) {
					// console.error('获取明细失败:', error)
					// this.$u.toast('获取明细数据失败')
				} finally {
					uni.hideLoading()
				}
			},
			// 备货操作
			async stockUp() {
			  const totalQty = this.detailList.reduce((sum, item) => sum + item.qty, 0);
			  const totalNowOutQty = this.detailList.reduce((sum, item) => sum + item.nowOutQty, 0);
			  
			  console.log("现在已出数量", totalNowOutQty);
			  console.log(this.detailList, "this.detailList");
			  console.log("总备货数量:", totalQty);
			  
			  // 检查是否所有项都已扫描（nowOutQty等于qty）
			  if (totalNowOutQty !== totalQty) {
			    uni.showToast({
			      title: '请扫描所有本次出库箱',
			      icon: 'none',
			      duration: 2000
			    });
			    return; // 阻止后续操作
			  }
			  
			  console.log("备货条件满足，准备调出库单状态变为备货中");
			  console.log(this.detailList,"this.detailList"); // 确保在此条件内操作
			  console.log(this.detailList[0].stockOutId)
			  
			  try {
			    // 调用后端提交接口（等待 Promise 完成）
			    const result =await stockUpApi(this.detailList)
			    // 处理业务结果
			    if (result.code === 200) { 
			      // 成功：展示成功信息并返回上一页
			      this.$u.toast(result.msg);
				    // this.hasStockedUp = true; // 标记为已备货过
				            this.showStockButton = false; // 隐藏备货按钮
			      // setTimeout(() => {
			      //   uni.navigateBack();
			      // }, 1500);
			    }
			  } catch (error) {
			    // 处理网络异常（如断网、请求超时）
			  			 this.$u.toast(error.msg);
			    console.error('后端异常', error);
			  }
			  
			},
			

			// 扫描箱号
			scanContainer() {
				this.scanCode('containerNo', '箱号')
			},

			// 通用扫码方法
			scanCode(field, type) {
				uni.scanCode({
					success: (res) => {
						this.queryParams[field] = res.result
						this.$u.toast(`${type}扫码成功`)
					},
					fail: () => {
						this.$u.toast(`${type}扫码失败，请重试`)
					}
				})
			},
			// 箱号搜索
			async handleContainerSearch() {
				 // if (!this.disable) {
				 //                                        this.$u.toast('请先锁定库位')
				 //                                        return
				 //       
					  	try {					
				if (!this.queryParams.containerNo.trim()) {
					this.$u.toast('请输入栈板/箱号')
					return
				}
				const result = await produceApi.stockOutBox({
					qrCode:this.queryParams.containerNo,
					id:this.objData.id
				})
				if(result.code === 200) {
					this.locationData = result.data
					this.getProcureDetail()
				}else {
						this.$u.toast(result.msg || '获取数据失败');
					}
					}catch (error) {
					console.log('初始化失败:', error)
					this.$u.toast(error.msg);
				}
			},

			// 切换箱标签显示
			toggleBoxList(index) {
				console.log('222', this.visibleBoxIndex, index);
				this.visibleBoxIndex = this.visibleBoxIndex === index ? null : index
			},

			// 删除箱标签
			handleDeleteBox(boxId, itemIndex, boxIndex) {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除此箱标签吗？',
					success: async(res) => {
						if (res.confirm) {
							// this.item.boxList.splice(boxIndex, 1)
							const result = await produceApi.delStockBox(boxId)
							if(result.code === 200) {
								this.getProcureDetail()
							    this.showStockButton = true; // 隐藏备货按钮
								this.$u.toast('删除成功')
							}
							
						}
					}
				})
			},

			// 获取状态文本
			getLineStateText(state) {
				const itemState = this.outState.find(item => item.dictValue == state)
				return itemState.dictLabel || '未知状态'
			},
			getItemType(type) {
				const statusItem = this.typeDicts.find(item => item.dictValue === type)
				return statusItem?.dictLabel || '--'
			},
			// 获取状态类名
			getStatusClass(state) {
				const classes = {
					0: 'status-pending',
					1: 'status-partial',
					2: 'status-completed',
					3: 'status-canceled'
				}
				return classes[state] || ''
			},
			// 提交
			// async firstSubmit() {
			// 	  if (this.count == 0 || this.detailList.length == 0) {
			// 	                                        return this.$u.toast('未进行扫箱，无法提交')
			// 	                                }
			// 	// console.log(this.objData);
			// 	const result = await produceApi.submitSalesApp(this.objData)
			// 	if(result.code === 200) {
			// 		this.$u.toast(result.msg)
			// 		setTimeout(() => {
			// 			uni.navigateBack()
			// 		},1500)
			// 	}
			// }
		async firstSubmit() {
		  // 前置条件检查：未扫箱则提示并返回
		  if (this.count === 0 || this.detailList.length === 0) {
		    return this.$u.toast('未进行扫箱，无法提交');
		  }
		
		  try {
		    // 调用后端提交接口（等待 Promise 完成）
		    const result = await produceApi.submitSalesApp(this.objData);
		
		    // 处理业务结果
		    if (result.code === 200) { 
		      // 成功：展示成功信息并返回上一页
		      this.$u.toast(result.msg);
			    // 触发自定义事件通知首页刷新数据
			     uni.$emit('refreshStockOutList');
		      setTimeout(() => {
		        uni.navigateBack();
		      }, 1500);
		    }
		  } catch (error) {
		    // 处理网络异常（如断网、请求超时）
			 this.$u.toast(error.msg);
		    console.error('后端异常', error);
		  }
		}
			
		}
	}
</script>

<style lang="scss" scoped>
.procurement-detail {
  background-color: #f8f8f8; // 更浅的灰色背景
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 搜索栏样式 */
.search-header {
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.06);
  z-index: 100;

  .search-item {
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .search-label {
    width: 140rpx;
    color: #666;
    text-align: center;
    font-size: 28rpx;
  }
}

/* 内容区样式 */
.content-container {
  padding: 20rpx;
}

/* 单据信息卡片 */
.document-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 28rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .document-row {
    display: flex;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #888;
      width: 160rpx;
      flex-shrink: 0;
      font-weight: 500;
    }

    .value {
      color: #333;
      flex: 1;
      word-break: break-all;
    }
  }
}

/* 扫描统计 */
.scan-summary {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .label {
    color: #666;
    font-weight: 500;
  }

  .value {
    color: #2979ff;
    font-weight: bold;
    margin-left: 12rpx;
    font-size: 32rpx;
  }
}

/* 物料明细卡片 */
.detail-list {
  .detail-card {
    margin-bottom: 20rpx;
    border-radius: 16rpx;
    background-color: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    overflow: hidden;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx;
      border-bottom: 1rpx solid #f5f5f5;
      background-color: #f9f9f9;

      .title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
      }

      .status {
        padding: 6rpx 16rpx;
        border-radius: 24rpx;
        font-size: 24rpx;
        font-weight: 500;
        //待出库
        &.status-STOCK_OUT_PENDING {
          color: #ff9900;
          background-color: rgba(255, 153, 0, 0.1);
        }
        //已出库
        &.status-BE_OUT_STORAGE {
          color: #19be6b;
          background-color: rgba(25, 190, 107, 0.1);
        }
        //出库中
        &.status-PUT_OUT_STORAGE {
          color: #2979ff;
          background-color: rgba(41, 121, 255, 0.1);
        }
      }
    }

    .card-content {
      padding: 24rpx;

      .content-row {
        margin-bottom: 20rpx;
        font-size: 28rpx;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #888;
          font-weight: 500;
          width: 160rpx;
          display: inline-block;
        }

        .value {
          color: #333;
          font-weight: 400;
        }

        &.double,
        &.triple {
          display: flex;
          flex-wrap: wrap;

          .col {
            flex: 1;
            min-width: 50%;
            margin-bottom: 16rpx;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        &.triple .col {
          min-width: 33.33%;
        }
      }
    }

    .box-tags {
      border-top: 1rpx solid #f5f5f5;

      .box-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx;
        background-color: #f9f9f9;

        .title {
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
        }

        .u-icon {
          color: #999;
        }
      }

      .box-list {
        padding: 0 24rpx 24rpx;

        .box-item {
          position: relative;
          padding: 20rpx;
          margin-bottom: 16rpx;
          border: 1rpx solid #e8e8e8;
          border-radius: 12rpx;
          background-color: #fafafa;
          transition: all 0.2s;

          &:active {
            background-color: #f0f0f0;
          }

          .box-info {
            margin-bottom: 12rpx;
            font-size: 26rpx;
            display: flex;
            align-items: center;

            .label {
              color: #888;
              margin-right: 12rpx;
              min-width: 100rpx;
            }

            .value {
              color: #333;
              flex: 1;
              word-break: break-all;
            }
          }

          .box-action {
            position: absolute;
            right: 20rpx;
            bottom: 20rpx;

            .u-button {
              padding: 0 24rpx;
              height: 56rpx;
              line-height: 56rpx;
              font-size: 26rpx;
            }
          }
        }
      }
    }
  }
}

// .btns {
//   width: 100%;
//   background-color: #fff;
//   height: 100rpx;
//   position: fixed;
//   bottom: 0;
//   left: 0;
//   z-index: 99;
//   display: flex;
//   padding: 16rpx 24rpx;
//   box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.06);

//   .u-button {
//     flex: 1;
//     margin: 0 8rpx;
//     height: 68rpx;
//     line-height: 68rpx;
//     font-size: 30rpx;
//     border-radius: 34rpx;

//     &:first-child {
//       margin-left: 0;
//     }

//     &:last-child {
//       margin-right: 0;
//     }
//   }
// }
.btns {
  width: 100%;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 99;
  /* 移除高度限制 */
  padding: 16rpx 24rpx; 
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.06);
  /* 改为垂直布局 */
  display: flex;
  flex-direction: column;
}

.btn-container {
  /* 按钮之间增加垂直间距 */
  margin-bottom: 16rpx;
  /* 确保按钮宽度填满容器 */
  width: 100%;
}

.btn-container:last-child {
  /* 最后一个按钮去除底部间距 */
  margin-bottom: 0;
}

.u-button {
  /* 移除横向间距 */
  margin: 0 !important;
  width: 100%; /* 宽度填满容器 */
  height: 68rpx;
  line-height: 68rpx;
  font-size: 30rpx;
  border-radius: 34rpx;
}

/* 优化组件样式 */
::v-deep .u-search {
  &__content {
    &__input {
      background-color: #f5f5f5;
      border-radius: 12rpx;
      height: 72rpx;
      font-size: 28rpx;
    }

    &__action {
      background-color: #3c9cff;
      color: #fff;
      border-radius: 12rpx;
      padding: 0 24rpx;
      height: 72rpx;
      line-height: 72rpx;
      font-size: 28rpx;
    }
  }
}

::v-deep .u-button--mini {
  height: 56rpx;
  line-height: 56rpx;
  font-size: 26rpx;
  padding: 0 24rpx;
}

::v-deep .u-list {
  height: 100% !important;
}

::v-deep .u-loadmore {
  padding: 24rpx 0;
}

/* 空状态 */
::v-deep .u-empty {
  padding: 100rpx 0;
}
</style>