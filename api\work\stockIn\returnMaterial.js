import request from '@/config/request.js';

// // 获取生产退料列表
export const getList = (params) => request.get('system/stock_in/listByInspected', params)

// 获取生产退料列表详情
export const stockInDetail = (id) => request.get('/system/stock_in/' + id)

// 获取生产退料库位查询
export const stockCode = (data) => request.post('/system/stock_in/binCode' , data)

// 生产退料根据主表id查询列表及暂存
export const detailList = (id) => request.get('/system/return_materials/detail/list' , id)

// 生产退料扫箱号
export const stockInBo = (data) => request.post('/system/stock_in/getStockInBox' , data)

// 生产退料提交
export const stockInSubmit = (data) => request.post('/system/stock_in/submit' , data)

// 删除入库单箱信息
export const delStockBox = (id) => request.delete('/system/stock_box/' + id)


