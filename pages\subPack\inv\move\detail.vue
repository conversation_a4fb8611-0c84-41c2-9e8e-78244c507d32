<template>
	<view class="procurement-detail">
		<!-- 固定搜索栏 -->
		<u-sticky>
			<view class="search-header"
				v-if="!['cancellation', 'FINISH'].includes(objData.allotState)">
				<!-- 库位搜索 -->
				<view class="search-item">
					<u-search v-model="queryParams.locationCode" placeholder="请输入或扫描库位编号" :showAction="false"
						actionText="搜索" shape="square" :disabled="disable" searchIcon="scan" @clickIcon="scanLocation"
						@search="locationSearch" @custom="locationSearch">
						<template #label>
							<view class="search-label">库位</view>
						</template>
					</u-search>
				</view>

				<!-- 栈板/箱号搜索 -->
				<view class="search-item">
					<u-search v-model="queryParams.containerNo" placeholder="请输入或扫描栈板/箱号" :showAction="false"
						actionText="搜索" shape="square" searchIcon="scan" @clickIcon="scanContainer"
						@search="containerSearch" @custom="containerSearch">
						<template #label>
							<view class="search-label">栈板/箱号</view>
						</template>
					</u-search>
				</view>
				<view style="    text-align: center;
				background: #46a1ff;
			   
				border-radius: 40rpx;
				color: white;"  @click="jiebang">解绑库位</view>
			</view>
		</u-sticky>

		<!-- 主内容区 -->
		<view class="content-container">
			<!-- 单据信息卡片 -->
			<view class="document-card">
				<view class="document-row">
					<text class="label">调拨单号：</text>
					<text class="value">{{objData.allotNo}}</text>
				</view>
				<!-- <view class="document-row">
					<text class="label">收货单号：</text>
					<text class="value">{{objData.reviceNo}}</text>
				</view> -->
				<view class="document-row">
					<text class="label">经手人：</text>
					<text class="value">{{objData.handledBy}}</text>
				</view>
			</view>

			<!-- 扫描统计 -->
			<view class="scan-summary"
				v-if="!['cancellation', 'FINISH'].includes(objData.allotState)">
				<text class="label">实扫箱数：</text>
				<text class="value">{{count}}</text>
			</view>

			<u-divider />

			<!-- 物料明细卡片 -->
			<view class="detail-list">
				<u-list v-if="detailList.length > 0">
					<u-list-item v-for="(item, index) in detailList" :key="item.id">
						<view class="detail-card">
							<view class="card-header">
								<text class="title">序号 {{ index + 1 }}</text>
								<text class="status"
									:class="'status-' + item.allotState">{{ getStatusText(item.allotState) }}</text>
							</view>
							<view class="card-content">
								<view class="content-row">
									<text class="label">物料编号：</text>
									<text class="value">{{ item.materialCode || '--' }}</text>
								</view>
								<view class="content-row">
									<text class="label">物料名称：</text>
									<text class="value">{{ item.materialName || '--' }}</text>
								</view>
								<view class="content-row">
									<text class="label">规格型号：</text>
									<text class="value">{{ item.specification || '无' }}</text>
								</view>
								<view class="content-row double">

									<view class="col">
										<text class="label">单位：</text>
										<text class="value">{{ item.materialUnit || '--' }}</text>
									</view>
								</view>
								<view class="content-row triple">
									<view class="col">
										<text class="label">数量：</text>
										<text class="value">{{ item.qty || 0 }}</text>
									</view>
									<view class="col">
										<text class="label">已调拨：</text>
										<text class="value">{{ item.incomingQty || 0 }}</text>
									</view>
									<view class="col"
										v-if="!['cancellation', 'FINISH'].includes(objData.allotState)">
										<text class="label">本次调拨：</text>
										<text class="value">{{ item.timeQty || 0 }}</text>
									</view>
								</view>
							</view>
							<!-- 箱标签 -->
							<view class="box-tags" v-if="item.wmsAllotBoxList.length > 0">
								<view class="box-header" @click="toggleBoxList(index)">
									<text class="title">箱标签</text>
									<u-icon :name="visibleBoxList[index] ? 'arrow-up' : 'arrow-down'"
										size="20"></u-icon>
								</view>

								<view class="box-list" v-show="visibleBoxList[index]">
									<view class="box-item" v-for="(box, boxIndex) in item.wmsAllotBoxList"
										:key="box.id">
										<view class="box-info">
											<text class="label">箱号：</text>
											<text class="value">{{ box.boxNo || '--' }}</text>
										</view>
										<view class="box-info">
											<text class="label">数量：</text>
											<text class="value">{{ box.qty || 0 }}</text>
										</view>
										<view class="box-action">
											<u-button type="error" size="mini"
												@click="handleDeleteBox(box.id)">删除</u-button>
										</view>
									</view>

									<u-loadmore v-if="item.boxList.length > 0"
										:status="item.boxLoadStatus || 'loadmore'" />
								</view>
							</view>
						</view>
					</u-list-item>

					<u-loadmore :status="detailLoadStatus" />
				</u-list>

				<u-empty v-else mode="list" />
			</view>
		</view>
		<view class="btns" v-if="!['cancellation', 'FINISH'].includes(objData.allotState)">
			<u-button type="primary" size="small" :throttleTime="1000" @tap="firstSubmit">提交</u-button>
			<u-button type="info" size="small">取消</u-button>
		</view>
	</view>
</template>

<script>
	import * as allotApi from '@/api/work/inv/allot.js'
	import {
		getDicts
	} from '@/api/dict/dict'
	export default {
		data() {
			return {
				queryParams: {
					locationCode: '', // 库位编号
					containerNo: '', // 栈板/箱号
					allotNo: '' // 调拨单号
				},
				loading: false,
				// visibleBoxIndex: null, // 当前展开的箱标签索引
				visibleBoxList: [], // 改为存储每个项展开状态的数组
				detailList: [],
				objData: {}, //页面传参
				locationData: {}, //库位数据
				disable: false, //库位是否可输入
				count: 0,
				statusDict: []
			}
		},
		async onLoad(options) {
			try {
				const item = JSON.parse(decodeURIComponent(options.params))
				console.log(item.id);
				this.objData = item
				this.getAllotDetail()
				this.getDict()
			} catch (error) {
				// console.error('初始化失败:', error)
				this.$u.toast('参数解析失败')
			}
		},
		methods: {
			jiebang(){
				this.disable=false;
				this.queryParams.locationCode = '';
				this.$u.toast('库位已解绑')
			},
			async getDict() {
				const res = await getDicts('allot_state')
				this.statusDict = res.data || []
			},
			// 调拨详情
			async getAllotDetail() {
				const result = await allotApi.detailList({
					allotId: this.objData.id
				})
				if (result.code === 200) {
					// const rows = result.data.wmsStockInDetails || []
					const rows = result.data.wmsAllotDetails || []
					this.count = result.data.count
					// 初始化明细数据，添加boxList字段
					this.detailList = rows.map(item => ({
						...item,
						boxList: item.wmsAllotBoxList,
						boxLoadStatus: 'loadmore',
						boxPageNum: 1
					}))
					console.log(this.detailList,'detailList');
					
					this.detailLoadStatus = rows.length < this.queryParams.pageSize ? 'nomore' : 'loadmore'
				// 初始化每个箱标签的展开状态
				    this.visibleBoxList = new Array(this.detailList.length).fill(false)
				}
			},
			// 扫描库位
			scanLocation() {
				this.scanCode('locationCode', '库位')
			},

			// 扫描箱号
			scanContainer() {
				this.scanCode('containerNo', '箱号')
			},

			// 通用扫码方法
			scanCode(field, type) {
				uni.scanCode({
					success: (res) => {
						this.queryParams[field] = res.result
						this.$u.toast(`${type}扫码成功`)
					},
					fail: () => {
						this.$u.toast(`${type}扫码失败，请重试`)
					}
				})
			},

			// 库位搜索
			async locationSearch() {
				if (!this.queryParams.locationCode.trim()) {
					this.$u.toast('请输入库位编号')
					return
				}
				await this.handleSearch('location')
			},

			// 箱号搜索
			async containerSearch() {
				
				if(!this.disable){
					this.$u.toast('请先绑定库位！')
					return
				}
				
				if (!this.queryParams.containerNo.trim()) {
					this.$u.toast('请输入栈板/箱号')
					return
				}

				await this.handleSearch('container')
			},

			// 通用搜索方法
			async handleSearch(type) {
				try {
					this.loading = true
					// 这里根据type调用不同API
					// const res = await allotApi.allotCode[type](this.queryParams)
					if (type == 'location') {
						const res = await allotApi.allotCode({
							locationCode: this.queryParams.locationCode,
							// 调拨主表id
							id: this.objData.id,
						})
						if (res.code === 200) {
							this.locationData = res.data
							this.disable = true
							this.$u.toast('库位已锁定！')
						}
					} else if (type == 'container') {
						if (this.locationData) {
							const params = {
								qrCode: this.queryParams.containerNo,
								id: this.objData.id,
								mdmWarehouse: this.locationData
							}
							// 扫箱号：stockInBo
							const result = await allotApi.allotBo(params)
							if (result.code === 200) {
								this.getAllotDetail()
								this.queryParams.containerNo = ''
							} else {
								this.$u.toast(result.msg)
							}
						} else {
							this.$u.toast('请输入库位编号')
						}
					}

				} catch (error) {
					console.error('搜索失败:', error)
					this.$u.toast(error.msg || '搜索失败')
				} finally {
					this.loading = false
				}
			},

			// 切换箱标签显示
			toggleBoxList(index) {
				 // 创建新数组确保响应式更新
				  const newVisibleList = [...this.visibleBoxList]
				  // 切换当前项的展开状态
				  newVisibleList[index] = !newVisibleList[index]
				  this.visibleBoxList = newVisibleList
			},

			// 删除箱标签
			handleDeleteBox(boxId) {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除此箱标签吗？',
					success: async (res) => {
						if (res.confirm) {
							// this.item.boxList.splice(boxIndex, 1)
							// allotApi.delStockBox,接口方法
							const result = await allotApi.delAllotBox(boxId)
							if (result.code === 200) {
								this.$u.toast('删除成功')
								this.getAllotDetail()
							}

						}
					}
				})
			},

			// 获取状态文本
			getStatusText(state) {
				const statusItem = this.statusDict.find(item => item.dictValue === state)
				return statusItem?.dictLabel || '未知状态'
			},
			// 提交
			async firstSubmit() {
				if (this.count == 0 || this.detailList.length == 0) {
					return this.$u.toast('请先扫描箱标签，再提交')
				}
				console.log(this.objData);
				// allotApi.stockInSubmit(this.objData),stockInSubmit:接口方法
				const result = await allotApi.allotSubmit(this.objData)
				if (result.code === 200) {
					this.$u.toast(result.msg)
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.procurement-detail {
		background-color: #f8f8f8; // 更浅的灰色背景
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	/* 搜索栏样式 */
	.search-header {
		padding: 20rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.06);
		z-index: 100;

		.search-item {
			// margin-bottom: 20rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}

		.search-label {
			width: 140rpx;
			color: #666;
			text-align: center;
			font-size: 28rpx;
		}
	}

	/* 内容区样式 */
	.content-container {
		padding: 20rpx;
	}

	/* 单据信息卡片 */
	.document-card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 28rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

		.document-row {
			display: flex;
			margin-bottom: 20rpx;
			font-size: 28rpx;
			line-height: 1.5;

			&:last-child {
				margin-bottom: 0;
			}

			.label {
				color: #888;
				width: 160rpx;
				flex-shrink: 0;
				font-weight: 500;
			}

			.value {
				color: #333;
				flex: 1;
				word-break: break-all;
			}
		}
	}

	/* 扫描统计 */
	.scan-summary {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding: 20rpx 0;
		font-size: 28rpx;
		background-color: #fff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

		.label {
			color: #666;
			font-weight: 500;
		}

		.value {
			color: #2979ff;
			font-weight: bold;
			margin-left: 12rpx;
			font-size: 32rpx;
		}
	}

	/* 物料明细卡片 */
	.detail-list {
		.detail-card {
			margin-bottom: 20rpx;
			border-radius: 16rpx;
			background-color: #fff;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
			overflow: hidden;

			.card-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx;
				border-bottom: 1rpx solid #f5f5f5;
				background-color: #f9f9f9;

				.title {
					font-size: 30rpx;
					font-weight: bold;
					color: #333;
				}

				.status {
					padding: 6rpx 16rpx;
					border-radius: 24rpx;
					font-size: 24rpx;
					font-weight: 500;

					//待调拨
					&.status-ENTRY_COMPLETED {
						color: #ff9900;
						background-color: rgba(255, 153, 0, 0.1);
					}

					//调拨完成
					&.status-FINISH {
						color: #19be6b;
						background-color: rgba(25, 190, 107, 0.1);
					}

					//调拨中
					&.status-IN_STORAGE {
						color: #2979ff;
						background-color: rgba(41, 121, 255, 0.1);
					}
				}
			}

			.card-content {
				padding: 24rpx;

				.content-row {
					margin-bottom: 20rpx;
					font-size: 28rpx;
					line-height: 1.5;

					&:last-child {
						margin-bottom: 0;
					}

					.label {
						color: #888;
						font-weight: 500;
						width: 160rpx;
						display: inline-block;
					}

					.value {
						color: #333;
						font-weight: 400;
					}

					&.double,
					&.triple {
						display: flex;
						flex-wrap: wrap;

						.col {
							flex: 1;
							min-width: 50%;
							margin-bottom: 16rpx;

							&:last-child {
								margin-bottom: 0;
							}
						}
					}

					&.triple .col {
						min-width: 33.33%;
					}
				}
			}

			.box-tags {
				border-top: 1rpx solid #f5f5f5;

				.box-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 24rpx;
					background-color: #f9f9f9;

					.title {
						font-size: 28rpx;
						font-weight: bold;
						color: #333;
					}

					.u-icon {
						color: #999;
					}
				}

				.box-list {
					padding: 0 24rpx 24rpx;

					.box-item {
						position: relative;
						padding: 20rpx;
						margin-bottom: 16rpx;
						border: 1rpx solid #e8e8e8;
						border-radius: 12rpx;
						background-color: #fafafa;
						transition: all 0.2s;

						&:active {
							background-color: #f0f0f0;
						}

						.box-info {
							margin-bottom: 12rpx;
							font-size: 26rpx;
							display: flex;
							align-items: center;

							.label {
								color: #888;
								margin-right: 12rpx;
								min-width: 100rpx;
							}

							.value {
								color: #333;
								flex: 1;
								word-break: break-all;
							}
						}

						.box-action {
							position: absolute;
							right: 20rpx;
							bottom: 20rpx;

							.u-button {
								padding: 0 24rpx;
								height: 56rpx;
								line-height: 56rpx;
								font-size: 26rpx;
							}
						}
					}
				}
			}
		}
	}

	.btns {
		width: 100%;
		background-color: #fff;
		height: 100rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 99;
		display: flex;
		padding: 16rpx 24rpx;
		box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.06);

		.u-button {
			flex: 1;
			margin: 0 8rpx;
			height: 68rpx;
			line-height: 68rpx;
			font-size: 30rpx;
			border-radius: 34rpx;

			&:first-child {
				margin-left: 0;
			}

			&:last-child {
				margin-right: 0;
			}
		}
	}

	/* 优化组件样式 */
	::v-deep .u-search {
		&__content {
			&__input {
				background-color: #f5f5f5;
				border-radius: 12rpx;
				height: 72rpx;
				font-size: 28rpx;
			}

			&__action {
				background-color: #3c9cff;
				color: #fff;
				border-radius: 12rpx;
				padding: 0 24rpx;
				height: 72rpx;
				line-height: 72rpx;
				font-size: 28rpx;
			}
		}
	}

	::v-deep .u-button--mini {
		height: 56rpx;
		line-height: 56rpx;
		font-size: 26rpx;
		padding: 0 24rpx;
	}

	::v-deep .u-list {
		height: 100% !important;
	}

	::v-deep .u-loadmore {
		padding: 24rpx 0;
	}

	/* 空状态 */
	::v-deep .u-empty {
		padding: 100rpx 0;
	}
</style>