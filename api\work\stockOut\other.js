import request from '@/config/request.js';

// 获取生产发料列表
export const otherList = (params) => request.get('/system/wmsOtherOut/list', params)

// 获取生产发料详情
export const otherDetail = (params) => request.get('/system/stockOutBox/scanCodet', params)




// 查询采购单列表
export const listPurchase = (query) => request.get('/system/purchase/list', query)

// 查询采购单明细列表  因后端接口修改，请将原有的result下的row 根据实际返回的内容 加一个层级
export const purchaseDetail = (query) => request.get('/system/purchase_detail/listAndStagingQty', query)
// export const purchaseDetail = (query) => request.get('/system/purchase_detail/list', query)

// 提交到货确认
export const submitPurchase = (id) => request.get('/system/purchase/submission/' + id)

// 查询箱标签列表
export const receiveBox = (query) => request.get('/system/receiveBox/list', query)

// 扫描箱标签
export const scanCode = (data) => request.post('/system/box/scanCode', data)

// 箱标签拆分箱标签二维码
export const autoCode = (data) => request.post('/system/box/getCode/qrCode', data)

// 箱标签拆分箱标签校验
export const scanSplitCode = (data) => request.post('/system/box/scanSplitCode' ,data)

// 标签拆分之后提交
export const splitSubmits = (data) => request.post('/system/box/splitSubmit' ,data)

//删除到货确认箱
export const deleteBox = (id) => request.delete('/system/receiveBox/'+id)