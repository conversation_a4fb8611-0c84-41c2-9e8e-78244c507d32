/* 自定义样式 */
.mobile-item-container {
  padding: 16rpx;
  background-color: #f8f8f8; // 更浅的灰色背景

  .header {
    background-color: #fff;
    padding: 20rpx;
    margin-bottom: 20rpx;
    border-radius: 8rpx;

    // 修改子组件样式
    ::v-deep .u-subsection {
      margin-top: 20rpx;
    }
  }


  .content {

position: relative;
.uni-card__header-extra {
	background-color: white;
}
    // 修改子组件样式
    ::v-deep .uni-card {
      margin: 20rpx 0 !important;
      border-radius: 12rpx;

     
    }
	.defauleState {
		    position: absolute;
		    padding: 4rpx 13rpx;
		    background-color: #00e813;
		    border-radius: 10rpx;
		    font-size: 24rpx;
		    color: #584c4c;
		    right: 16rpx;
		    top: 10rpx;
						}
	.executingState {
		    position: absolute;
		    padding: 4rpx 13rpx;
		    background-color: #19c8ba;
		    border-radius: 10rpx;
		    font-size: 24rpx;
		    color: #ffffff;
		    right: 16rpx;
		    top: 10rpx;
						}

						.createdState {
							    position: absolute;
							    padding: 4rpx 13rpx;
							    background-color: #2e8be8;
							    border-radius: 10rpx;
							    font-size: 24rpx;
							    color: #ffffff;
							    right: 16rpx;
							    top: 10rpx;
						}
    .card-content-item {
      margin-bottom: 12rpx;
      font-size: 28rpx;
      color: rgb(17, 17, 17);

      span {
        color: rgb(17, 17, 17);
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.uni-card .uni-card__header .uni-card__header-content .uni-card__header-content-title {
  font-size: 15px;
  color: #3a3a3a;
  font-weight: 600;
}
.uni-card .uni-card__header .uni-card__header-extra .uni-card__header-extra-text {
    font-size: 12px;
}

.status-BE_IN_STORAGE ,.status-BE_OUT_STORAGE{
	::v-deep .uni-card__header-extra-text{
		border-radius: 10rpx;
		padding: 10rpx 20rpx;
		color: #19be6b !important;
		background-color: rgba(25, 190, 107, 0.2) !important;
	}
  
}
.status-STOCK_PENDING ,.status-STOCK_OUT_PENDING{
	::v-deep .uni-card__header-extra-text{
		border-radius: 10rpx;
		padding: 10rpx 20rpx;
		color: #ff9900 !important;
		background-color: rgba(255, 153, 0, 0.1) !important;
	}
  
}
//入库中
.status-PUT_STORAGE ,.status-PUT_OUT_STORAGE {
	::v-deep .uni-card__header-extra-text{
		border-radius: 10rpx;
		padding: 10rpx 20rpx;
		color: #2979ff !important;
		background-color: rgba(41, 121, 255, 0.1) !important;
	}
  
}

