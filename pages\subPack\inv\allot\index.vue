<template>
	<view class="mobile-item-container">
		<view class="header">
			<u-search v-model="queryParams.allotNo" :showAction="true" actionText="搜索" placeholder="请输入调拨单号"
				:animation="true" shape="square" @search="handleSearch" @custom="handleSearch" />
			<u-subsection :list="subList" mode="subsection" :current="currentTab" @change="handleTabChange" />
		</view>

		<view class="content">
			<uni-card v-for="item in allotList" :key="item.id" :title="item.allotNo"
				:extra="getStatusText(item.allotState)" @click="handleItemClick(item)" :class="'status-' + item.allotState">
				<view class="card-content-item">
					<span>经&ensp;手&ensp;人：</span>
					<text>{{ item.handledBy || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>调拨日期：</span>
					<text>{{ item.allotDate || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>创建时间：</span>
					<text>{{ item.createTime || '--' }}</text>
				</view>
			</uni-card>

			<u-empty v-if="!loading && allotList.length === 0" mode="list" />
		</view>

		<u-loadmore v-if="allotList.length > 0" :status="loadStatus" />
	</view>
</template>

<script>
	import {
		listAllot
	} from '@/api/work/inv/allot'
	import {
		getDicts
	} from '@/api/dict/dict'
	// import { formatDate } from '@/utils/date'

	export default {
		data() {
			return {
				subList: ['待办', '已办'],
				currentTab: 0,
				allotList: [],
				statusDict: [],
				loading: false,
				loadStatus: 'loadmore',
				queryParams: {
					pageNum: 1,
					pageSize: 10,
                    allotStateArr: ['ENTRY_COMPLETED','IN_STORAGE']
				},
				total: 0
			}
		},

		async created() {
			await this.loadInitialData()
		},

		onReachBottom() {
			if (this.queryParams.pageNum * this.queryParams.pageSize < this.total) {
				this.queryParams.pageNum++
				this.loadData()
			}
		},

		methods: {
			async loadInitialData() {
				try {
					this.loading = true
					await Promise.all([this.loadDictData(), this.loadData()])
				} catch (error) {
					this.handleError(error, '初始化数据失败')
				} finally {
					this.loading = false
				}
			},

			async loadDictData() {
				try {
					const res = await getDicts('allot_state')
					this.statusDict = res.data || []
				} catch (error) {
					this.handleError(error, '获取字典数据失败')
				}
			},

			async loadData() {
				try {
					this.loadStatus = 'loading'
					const res = await listAllot(this.queryParams)

					if (res.code === 200) {
						this.total = res.total
						if (this.queryParams.pageNum === 1) {
							this.allotList = res.rows || []
						} else {
							this.allotList = [...this.allotList, ...(res.rows || [])]
						}

						this.loadStatus = res.rows.length < this.queryParams.pageSize ? 'nomore' : 'loadmore'
					} else {
						this.showToast(res.msg || '获取数据失败', 'none')
					}
				} catch (error) {
					this.handleError(error, '获取列表数据失败')
				}
			},

			handleSearch() {
				this.queryParams.pageNum = 1
				this.loadData()
			},

			handleTabChange(index) {
				this.currentTab = index
				this.queryParams = {
					...this.queryParams,
					pageNum: 1,
					allotStateArr: index === 0 ? ['ENTRY_COMPLETED','IN_STORAGE'] :
						['FINISH'] // Replace with actual state values
				}
				this.loadData()
				
				
				
			},

			getStatusText(state) {
				const statusItem = this.statusDict.find(item => item.dictValue === state)
				if (statusItem) {
				// 动态设置颜色（需结合 DOM 操作或样式变量）
				this.$nextTick(() => {
				  const card = this.$refs.card; // 假设给 uni-card 添加 ref
				  card.style.backgroundColor = statusItem.color;
				});
			  }
				return statusItem?.dictLabel || '未知状态'
			},

			handleItemClick(item) {
				console.log(item, 'item');
				uni.navigateTo({
					url: `/pages/subPack/inv/allot/detail?params=${encodeURIComponent(JSON.stringify(item))}`
				})
			},

			formatTime(time) {
				return time ? formatDate(time, 'YYYY-MM-DD HH:mm:ss') : ''
			},

			handleError(error, message) {
				console.error(message, error)
				this.showToast(message, 'none')
			},

			showToast(message, icon = 'none') {
				uni.showToast({
					title: message,
					icon
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.mobile-item-container {
		padding: 16rpx;
		min-height: 100vh;

		.header {
			background-color: #fff;
			padding: 20rpx;
			margin-bottom: 20rpx;
			border-radius: 8rpx;

			::v-deep .u-subsection {
				margin-top: 20rpx;
			}
		}

		.content {
			::v-deep .uni-card {
				margin: 20rpx 0 !important;
				border-radius: 12rpx;

				.uni-card__header-extra {
					padding: 8rpx 16rpx;
					// background-color: #ffcc00;
					border-radius: 8rpx;
					color: #333 !important;
					font-size: 24rpx;
				}
			}
			
			// 在样式中定义不同状态对应的颜色
			// ENTRY_COMPLETED:待调拨，黄色
			.status-ENTRY_COMPLETED ::v-deep .uni-card__header-extra {
			  // background-color: #ff9900; 
			  // color: white !important;
			  background-color: rgba(255, 153, 0, 0.1);
			  color: #ff9900 !important;
			  
			}
			// IN_STORAGE:调拨中，蓝色
			.status-IN_STORAGE ::v-deep .uni-card__header-extra {
			  // background-color: #2979ff;
			  // color: white !important;	  
			  color: #2979ff !important;
			  background-color: rgba(41, 121, 255, 0.1);
			}
			// FINISH:调拨完成，绿色
			.status-FINISH ::v-deep .uni-card__header-extra {
			  // background-color: #19be6b; 
			  // color: white !important;
			  color: #19be6b !important;
			  background-color: rgba(25, 190, 107, 0.1);
			}
			.card-content-item {
				margin-bottom: 12rpx;
				font-size: 28rpx;
				color: #666;

				span {
					color: #999;
				}

				&:last-child {
					margin-bottom: 0;
				}
			}
		}
		
	}
</style>