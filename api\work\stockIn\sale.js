import request from '@/config/request.js';

// 获取销售退货列表
export const listStockIn = (params) => request.get('system/stock_in/listByInspected', params)

// 获取销售退货列表详情
export const stockInDetail = (id) => request.get('/system/stock_in/' + id)

// 获取销售退货库位查询
export const stockCode = (data) => request.post('/system/stock_in/binCode' , data)

// 销售退货根据主表id查询列表及暂存
export const detailList = (id) => request.get('/system/wms_sales_return/detail/list' , id)

// 销售退货扫箱号
export const stockInBo = (data) => request.post('/system/stock_in/getStockInBox' , data)

// 销售退货提交
export const stockInSubmit = (data) => request.post('/system/stock_in/submit' , data)

// 删除入库单箱信息
export const delStockBox = (id) => request.delete('/system/stock_box/' + id)
