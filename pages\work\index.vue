<template>
	<view>
		<!-- 顶部导航栏 -->
		<Navbar title="工作台" bgColor="#fff" :hideBtn="true" :h5Show="false"></Navbar>
		<!-- 采购管理 -->
		<view class="" v-for="(item,index) in menuList" :key="index">
      
			<view class="" v-if="item.children && item.name != 'System' && item.name != 'Tool'">
				<view class="mobile-item-container">
					<view class="section-title">{{item.meta.title}}</view>
					<u-grid col="3">
						<u-grid-item v-for="(i,e) in item.children" :key="e" @click="onClick(item.path,i.path)"
							>
							<!-- <view class="icon iconfont">{{i.meta.appIcon}}</view> -->
							<text class="iconfont" :class="i.meta.appIcon"></text>
							<text class="btn-text">{{i.meta.title}}</text>
						</u-grid-item>
					</u-grid>
				</view>
				<Gap />
			</view>
		</view>
	</view>
</template>

<script>
	import Navbar from '@/components/navbar/Navbar';
	import Gap from '@/components/gap/Gap';
	import * as routeApi from '@/api/router.js';

	export default {
		components: {
			Gap,
			Navbar,
		},
		data() {
			return {
				menuList: [], //菜单数据
			};
		},
		onLoad() {
			this.getRoute();
		},
		methods: {
			// 获取菜单数据
			async getRoute() {
				uni.showLoading({
					title: '加载中...'
				})
				try {
					const result = await routeApi.getRouter('APP');
					if (result.code === 200) {
						this.menuList = result.data
					}
				} catch (error) {
					console.error('获取路由失败:', error);
				} finally {
					uni.hideLoading()
				}
			},
			// 标签页切换
			tabChange(item) {
				this.activeKey = item.key;
			},
			// 页面跳转
			onClick(item, i) {
				const url = '/pages/subPack' + item + '/' + i
				console.log(url);
				uni.navigateTo({
					url
					
				});
			},
		},
	};
</script>

<style lang="scss">
	.mobile-item-container {
		padding: 20rpx;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		padding-bottom: 20rpx;
		padding-left: 20rpx;
	}

	.icon-container {
		position: relative;
	}

	.badge {
		position: absolute;
		top: -5rpx;
		right: -10rpx;
	}

	.btn-text {
		margin-top: 10rpx;
		text-align: center;
		font-size: 28rpx;
	}

	.tabs-container {
		padding: 40rpx;
		background-color: #fff;
		margin-top: 40rpx;
		min-height: 600rpx;
	}

	.tab-content {
		padding: 40rpx 0;
	}

	.grid-margin {
		margin-top: 40rpx;
	}

	.u-grid {
		justify-content: center;

		.u-grid-item {
			height: 160rpx;
      border-radius: 10px;
      border: 1px solid rgb(224, 224, 224);
			width: 30% !important;
			margin: 10rpx;
		}
	}
</style>