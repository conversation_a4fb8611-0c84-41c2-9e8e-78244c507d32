{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/work/index",
			"style": {
				"navigationBarTitleText": "工作台",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/index",
			"style": {
				"navigationBarTitleText": "登录",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom"
			}
		},

		{
			"path": "pages/work/user/list",
			"style": {
				"navigationBarTitleText": "用户管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/work/user/edit",
			"style": {
				"navigationBarTitleText": "用户详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/work/notice/list",
			"style": {
				"navigationBarTitleText": "通知公告",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/work/notice/manage",
			"style": {
				"navigationBarTitleText": "公告管理",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/work/notice/detail",
			"style": {
				"navigationBarTitleText": "公告详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/work/notice/edit",
			"style": {
				"navigationBarTitleText": "公告详情",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/center/index",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/center/profile",
			"style": {
				"navigationBarTitleText": "个人资料",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/center/log",
			"style": {
				"navigationBarTitleText": "操作日志",
				"navigationStyle": "custom"
			}
		}
	],
	"subPackages": [{
		"root": "pages/subPack",
		"pages": [{
				"path": "label/split",
				"style": {
					"navigationBarTitleText": "标签拆分"
				}
			},
			{
				"path": "stockIn/production/detail",
				"style": {
					"navigationBarTitleText": "生产入库详情"
				}
			},
			{
				"path": "stockIn/other/detail",
				"style": {
					"navigationBarTitleText": "其他入库详情"
				}
			},
			{
				"path": "stockIn/outsourcing/detail",
				"style": {
					"navigationBarTitleText": "委外发货详情"
				}
			},
			{
				"path": "label/regroup",
				"style": {
					"navigationBarTitleText": "标签重组"
				}
			},
			{
				"path": "wms/confirmation/index",
				"style": {
					"navigationBarTitleText": "到货确认"
				}
			},
			{
				"path": "wms/confirmation/detail",
				"style": {
					"navigationBarTitleText": "到货确认详情"
				}
			},
			{
				"path": "stockIn/procurement/index",
				"style": {
					"navigationBarTitleText": "采购入库"
				}
			},
			{
				"path": "stockIn/production/index",
				"style": {
					"navigationBarTitleText": "生产入库"
				}
			},
			{
				"path": "stockIn/stripping/index",
				"style": {
					"navigationBarTitleText": "生产退料"
				}
			},
			{
				"path": "stockIn/sale/index",
				"style": {
					"navigationBarTitleText": "销售退货"
				}
			},
			{
				"path": "stockIn/sale/detail",
				"style": {
					"navigationBarTitleText": "销售退货详情"
				}
			},
			{
				"path": "stockOut/purchaseReturns/detail",
				"style": {
					"navigationBarTitleText": "采购退货详情"
				}
			},
			{
				"path": "stockIn/other/index",
				"style": {
					"navigationBarTitleText": "其他入库"
				}
			},
			{
				"path": "stockIn/outsourcing/index",
				"style": {
					"navigationBarTitleText": "委外退货"
				}
			},
			// {
			// 	"path": "stockIn/production/detail",
			// 	"style": {
			// 		"navigationBarTitleText": "生产入库详情"
			// 	}
			// },
			{
				"path": "stockIn/procurement/detail",
				"style": {
					"navigationBarTitleText": "采购入库"
				}
			},
			{
				"path": "stockOut/produceIssue/index",
				"style": {
					"navigationBarTitleText": "生产发料"
				}
			},
			{
				"path": "stockOut/produceIssue/detail",
				"style": {
					"navigationBarTitleText": "生产发料详情"
				}
			},
			{
				"path": "stockOut/saleShipment/index",
				"style": {
					"navigationBarTitleText": "销售发货"
				}
			},
			{
				"path": "stockOut/saleShipment/detail",
				"style": {
					"navigationBarTitleText": "销售发货详情"
				}
			},
			{
				"path": "stockOut/purchaseReturns/index",
				"style": {
					"navigationBarTitleText": "采购退货"
				}
			},
			{
				"path": "stockOut/otherOutbound/index",
				"style": {
					"navigationBarTitleText": "其他出库"
				}
			},
			{
				"path": "stockOut/otherOutbound/detail",
				"style": {
					"navigationBarTitleText": "其他出库详情"
				}
			},
			{
				"path": "stockOut/outsourced/index",
				"style": {
					"navigationBarTitleText": "委外发货"
				}
			},
			{
				"path": "stockOut/outsourced/detail",
				"style": {
					"navigationBarTitleText": "委外发货详情"
				}
			},
			{
				"path": "stockIn/returnMaterial/index",
				"style": {
					"navigationBarTitleText": "生产退料"
				}
			},
			{
				"path": "stockIn/returnMaterial/detail",
				"style": {
					"navigationBarTitleText": "生产退料详情"
				}
			},
			{
				"path": "inv/allot/index",
				"style": {
					"navigationBarTitleText": "调拨管理"
				}
			},
			{
				"path": "inv/allot/detail",
				"style": {
					"navigationBarTitleText": "调拨管理详情"
				}
			},
			{
				"path": "inv/move/index",
				"style": {
					"navigationBarTitleText": "物料移库"
				}
			},
			{
				"path": "inv/move/detail",
				"style": {
					"navigationBarTitleText": "物料移库详情"
				}
			}
		]
	}],
	"tabBar": {
		"color": "#909399",
		"selectedColor": "#000000",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"list": [{
			"pagePath": "pages/index/index",
			"iconPath": "static/img/tabbar/home.png",
			"selectedIconPath": "static/img/tabbar/home_art.png",
			"text": "首页"
		}, {
			"pagePath": "pages/work/index",
			"iconPath": "static/img/tabbar/center.png",
			"selectedIconPath": "static/img/tabbar/center_art.png",
			"text": "工作台"
		}, {
			"pagePath": "pages/center/index",
			"iconPath": "static/img/tabbar/user.png",
			"selectedIconPath": "static/img/tabbar/user_art.png",
			"text": "我的"
		}]
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"app-plus": {
			"softinputMode": "adjustResize"
		}
	},
	"easycom": {
		"^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
	}
}