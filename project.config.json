{"appid": "wxd52fd16e8f29ac8e", "compileType": "miniprogram", "libVersion": "3.8.2", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "urlCheck": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useMultiFrameRuntime": true, "packNpmManually": false, "minifyWXSS": true, "useStaticServer": true, "showES6CompileOption": false, "checkInvalidKey": true, "disableUseStrict": false, "useCompilerPlugins": false, "minifyWXML": true, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "miniprogramRoot": "unpackage/dist/dev/mp-weixin/", "srcMiniprogramRoot": "unpackage/dist/dev/mp-weixin/", "description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "simulatorPluginLibVersion": {}}