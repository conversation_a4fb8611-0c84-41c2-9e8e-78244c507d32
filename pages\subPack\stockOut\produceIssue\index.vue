<template>
	<view class="mobile-item-container">
		<view class="header">
			<u-search v-model="queryParams.stockOutNo" :showAction="true" actionText="搜索" placeholder="请输入出库单号"
				:animation="true" shape="square" @search="handleSearch" @custom="handleSearch" />
			<u-subsection :list="subList" mode="subsection" :current="currentTab" @change="handleTabChange" />
		</view>

		<view class="content">
			<uni-card v-for="item in procureList" :key="item.id" :title="`出库单号：  ` + item.stockOutNo"
				:extra="getStatusText(item.stockOutState)" @click="handleItemClick(item)" :class="'status-' + item.stockOutState">
				<!-- <view class="card-content-item">
					<span>物料名称：</span>
					<text>{{ item.materialName || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>物料编码：</span>
					<text>{{ item.materialCode || '--' }}</text>
				</view> -->
				<view class="card-content-item">
					<span>出库类型：</span>
					<text>{{ getItemType(item.stockOutType) }}</text>
				</view>
				<!-- <view class="card-content-item">
					<span>供&ensp;应&ensp;商：</span>
					<text>{{ item.supplierName || '--' }}</text>
				</view> -->
				<!-- <view class="card-content-item">
					<span>仓库名称：</span>
					<text>{{ item.warehouseName || '--' }}</text>
				</view> -->
				<view class="card-content-item">
					<span>出库日期：</span>
					<text>{{ item.stockOutDate || '--' }}</text>
				</view>
				<view class="card-content-item">
					<span>创建时间：</span>
					<text>{{ item.createTime || '--' }}</text>
				</view>
			</uni-card>

			<u-empty v-if="!loading && procureList.length === 0" mode="list" />
		</view>

		<u-loadmore v-if="procureList.length > 0" :status="loadStatus" />
	</view>
</template>

<script>
	import * as produceApi from '@/api/work/stockOut/produce.js'
	import {
		getDicts
	} from '@/api/dict/dict'
	// import { formatDate } from '@/utils/date'

	export default {
		data() {
			return {
				subList: ['待办', '已办'],
				currentTab: 0,
				procureList: [],
				statusDict: [],
				loading: false,
				loadStatus: 'loadmore',
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					stockOutType:'REQUISITION',
          stockOutStateArr: ["STOCK_OUT_PENDING","PUT_OUT_STORAGE"],
				},
				total: 0,
				typeDicts:[]
			}
		},

		async created() {
			await this.loadInitialData()
		// 监听自定义事件
		        uni.$on('refreshStockOutList', this.handleRefreshList)
		    },
		    beforeDestroy() {
		        // 销毁事件监听
		        uni.$off('refreshStockOutList', this.handleRefreshList)
		    },

		onReachBottom() {
			if (this.queryParams.pageNum * this.queryParams.pageSize < this.total) {
				this.queryParams.pageNum++
				this.loadData()
			}
		},

		methods: {
			async loadInitialData() {
				try {
					this.loading = true
					await Promise.all([this.loadDictData(), this.loadData()])
				} catch (error) {
					this.handleError(error, '初始化数据失败')
				} finally {
					this.loading = false
				}
			},

			async loadDictData() {
				try {
					const res = await getDicts('stock_out_state')
					this.statusDict = res.data || []
					const result = await getDicts('stock_out_type')
					this.typeDicts = result.data || []
				} catch (error) {
					this.handleError(error, '获取字典数据失败')
				}
			},

			async loadData() {
				try {
					this.loadStatus = 'loading'
					const res = await produceApi.produceList(this.queryParams)

					if (res.code === 200) {
						this.total = res.total
						if (this.queryParams.pageNum === 1) {
							this.procureList = res.rows || []
						} else {
							this.procureList = [...this.procureList, ...(res.rows || [])]
						}

						this.loadStatus = res.rows.length < this.queryParams.pageSize ? 'nomore' : 'loadmore'
					} else {
						this.showToast(res.msg || '获取数据失败', 'none')
					}
				} catch (error) {
					this.handleError(error, '获取列表数据失败')
				}
			},

			handleSearch() {
				this.queryParams.pageNum = 1
				this.loadData()
			},

			handleTabChange(index) {
				this.currentTab = index
				this.queryParams = {
					...this.queryParams,
					pageNum: 1,
					stockOutStateArr: index === 0 ? ['STOCK_OUT_PENDING','PUT_OUT_STORAGE'] :
						['BE_OUT_STORAGE']  // Replace with actual state values
				}
				this.loadData()
			},

			getStatusText(state) {
				const statusItem = this.statusDict.find(item => item.dictValue === state)
				return statusItem?.dictLabel || '未知状态'
			},
			getItemType(type) {
				const statusItem = this.typeDicts.find(item => item.dictValue === type)
				return statusItem?.dictLabel || '--'
			},
			handleItemClick(item) {
				console.log(item, 'item');
				uni.navigateTo({
					url: `/pages/subPack/stockOut/produceIssue/detail?params=${encodeURIComponent(JSON.stringify(item))}`
				})
			},

			formatTime(time) {
				return time ? formatDate(time, 'YYYY-MM-DD HH:mm:ss') : ''
			},

			handleError(error, message) {
				console.error(message, error)
				this.showToast(message, 'none')
			},

			showToast(message, icon = 'none') {
				uni.showToast({
					title: message,
					icon
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.mobile-item-container {
		padding: 16rpx;
		min-height: 100vh;
		background-color: #f5f5f5;

		.header {
			background-color: #fff;
			padding: 20rpx;
			margin-bottom: 20rpx;
			border-radius: 8rpx;

			::v-deep .u-subsection {
				margin-top: 20rpx;
			}
		}

		.content {
			::v-deep .uni-card {
				margin: 20rpx 0 !important;
				border-radius: 12rpx;

				.uni-card__header-extra {
					padding: 8rpx 16rpx;
					// background-color: #ffcc00;
					border-radius: 8rpx;
					color: #333 !important;
					font-size: 24rpx;
				}
			}

			.card-content-item {
				margin-bottom: 12rpx;
				font-size: 28rpx;
				color: #666;

				span {
					color: #999;
				}

				&:last-child {
					margin-bottom: 0;
				}
			}
		}
	}
</style>