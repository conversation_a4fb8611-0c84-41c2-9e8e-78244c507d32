<template>
	<view class="purchase-detail-container">
		<!-- 搜索框 -->
		<!-- <u-sticky>
			<view class="search-box">
				<u-search v-model="searchValue" placeholder="请扫码或者输入箱标签" searchIcon="scan" @clickIcon="handleScan"
					@search="handleSearch" @custom="handleSearch" :animation="true" shape="square" />
			</view>
		</u-sticky> -->
		<!-- 折叠面板 -->
		<u-collapse :value="collapseActive" @open="handleCollapseOpen">
			<!-- 采购单信息 -->
			<u-collapse-item name="info" title="采购单信息">
				<u-icon name="tags-fill" size="20" slot="icon"></u-icon>
				<view class="purchase-info">
					<view class="info-item">
						<text class="label">供应商编号：</text>
						<text class="value">{{ objData.supplierCode || "--" }}</text>
					</view>
					<view class="info-item">
						<text class="label">采购单号：</text>
						<text class="value">{{ objData.purchaseNo || "--" }}</text>
					</view>
					<view class="info-item">
						<text class="label">创建时间：</text>
						<text class="value">{{ objData.createTime || "--" }}</text>
					</view>
				</view>
				<!-- 扫描统计 -->
				<view class="scan-summary"
					v-if="!['BE_OUT_STORAGE', 'TO_BE_CANCELLATION', 'IN_STOCK', 'ISSTOP','ALLRETURN'].includes(objData.stockOutState)">
					<text class="label">实扫箱数：</text>
					<text class="value">{{count}}</text>
				</view>

			</u-collapse-item>
			<!-- 采购单明细 -->
			<u-collapse-item name="detail" title="采购单明细">
				<view class="detail-list">
					<u-list v-if="detailList.length > 0" 
					:key="listKey"
					height="calc(100vh - 400rpx)">
						<u-list-item v-for="(item, index) in detailList" :key="item.id">
							<view class="detail-card">
								<view class="card-header">
									<text class="title">序号 {{ item.purchaseLine }}</text>
									<text v-if="item.lineState=='CREATED'||item.lineState=='EXECUTING'"
										class="createdState">{{ getLineStateText(item.lineState) }}</text>
									<text v-else class="status">{{ getLineStateText(item.lineState) }}</text>
								</view>

								<view class="card-content">
									<view class="content-row">
										<text class="label">物料编号：</text>
										<text class="value">{{ item.partCode || "--" }}</text>
									</view>
									<view class="content-row">
										<text class="label">物料名称：</text>
										<text class="value">{{ item.partName || "--" }}</text>
									</view>
									<view class="content-row">
										<text class="label">规格型号：</text>
										<text class="value">{{ item.partSpecification || "--" }}</text>
									</view>
									<view class="content-row double">
										<view class="col">
											<text class="label">单位：</text>
											<text class="value">{{ item.uom || "--" }}</text>
										</view>
									</view>
									<view class="content-row triple">
										<view class="col">
											<text class="label">应收数量：</text>
											<text class="value">{{ item.qty || 0 }}</text>
										</view>
										<view class="col">
											<text class="label">已收：</text>
											<text class="value">{{ item.receivedNum || 0 }}</text>
										</view>
										<view class="col">
											<text class="label">本次收：</text>
											<text class="value">{{ item.currentQty || 0 }}</text>
										</view>
									</view>
								</view>

								<!-- 箱标签 -->
								<view class="box-tags" v-if="item.boxList.length > 0">
									<view class="box-header" @click="toggleBoxList(index)">
										<text class="title">箱标签</text>
										<u-icon :name="expandedItems.includes(index) ? 'arrow-up' : 'arrow-down'"
											size="20"></u-icon>
									</view>

									<view class="box-list" v-show="expandedItems.includes(index)">
										<view class="box-item" v-for="(box, boxIndex) in item.boxList" :key="box.id">
											<view class="box-info">
												<text class="label">箱号：</text>
												<text class="value">{{ box.boxNo || "--" }}</text>
											</view>
											<view class="box-info">
												<text class="label">数量：</text>
												<text class="value">{{ box.qty || 0 }}</text>
											</view>
											<view v-show="parentItemState!='FINISHED'" class="box-action" @click="handleDeleteBox(box.id, index, boxIndex)">
												<u-icon color="#fa090d" name="trash-fill"></u-icon>
												<!-- 删除箱的小图标 -->
											</view>
										</view>
									</view>
								</view>
							</view>
						</u-list-item>

						<u-loadmore :status="detailLoadStatus" />
					</u-list>

					<u-empty v-else mode="list" />
				</view>
			</u-collapse-item>
		</u-collapse>

		<!-- 底部操作按钮 -->
		<view class="action-buttons" :loading="submitting"
			v-if="objData.lineState == 'CREATED' || objData.lineState == 'EXECUTING'">
			<u-button type="primary" throttleTime="1000" @click="handleSubmit">提交</u-button>
		</view>
	</view>
</template>

<script>
	import {
		purchaseDetail,
		scanCode,
		receiveBox,
		deleteBox,
		submitPurchase,
	} from "@/api/work/purchase/purchase";
	import {
		getDicts
	} from "@/api/dict/dict";
	// import { formatDate } from '@/utils/date'

	export default {
		data() {
			return {
				objData: {}, // 采购单主数据
				detailList: [], // 明细数据
				searchValue: "", // 搜索值
				lineState: [], // 状态字典
				collapseActive: ['info','detail'], // 默认展开所有折叠项
				expandedItems: [], // 当前显示的箱标签索引
				queryParams: {
					pageNum: 1,
					pageSize: 10,
					purchaseNo: "",
					isStaging: "YES" //默认查已经暂存的
				},
				detailLoadStatus: "loadmore",
				submitting: false,
				count: 0,
				parentItemState:null,
				listKey:0,
			};
		},

		async onLoad(options) {
			try {
				const item = JSON.parse(decodeURIComponent(options.params));
				if (item) {
					this.parentItemState = item.lineState;
					if (item.lineState == "CREATED" || item.lineState == "EXECUTING") {
						this.queryParams.isStaging = "YES"
					} else {
						this.queryParams.isStaging = "NO"
					}
					this.objData = item;
					this.queryParams.purchaseNo = item.purchaseNo;
					await Promise.all([this.getDict(), this.getPurDetaiList()]);
				}
			} catch (error) {
				// console.error('初始化失败:', error)
				this.$u.toast("参数解析失败");
			}


		},

		methods: {
			// 获取字典数据
			async getDict() {
				try {
					const res = await getDicts("line_state_dict");
					this.lineState = res.data || [];
				} catch (error) {
					// console.error('获取字典失败:', error)
					this.$u.toast("获取字典数据失败");
				}
			},

			// 获取采购单明细+暂存箱数量
			async getPurDetaiList() {
				try {
					uni.showLoading({
						title: "加载中...",
					});
					const result = await purchaseDetail(this.queryParams);
					if (result.code === 200) {
						const rows = result.data.row || [];
						// 已扫箱数后端处理返回赋值
						this.count = result.data.stagingQty
						// 初始化明细数据，添加boxList字段
						this.detailList = rows.map((item) => ({
							...item,
							boxList: item.wmsReceiveBoxList,
							boxLoadStatus: "loadmore",
							boxPageNum: 1,
						}));

						this.detailLoadStatus =
							rows.length < this.queryParams.pageSize ? "nomore" : "loadmore";
					} else {
						this.$u.toast(result.data.msg || "获取数据失败");
					}
				} catch (error) {
					// console.error('获取明细失败:', error)
					this.$u.toast("获取明细数据失败");
				} finally {
					uni.hideLoading();
				}
			},

			// 加载更多明细
			async loadMoreDetails() {
				if (this.detailLoadStatus === "nomore") return;

				try {
					this.detailLoadStatus = "loading";
					this.queryParams.pageNum += 1;

					const result = await purchaseDetail(this.queryParams);
					if (result.code === 200) {
						const newRows = result.rows || [];
						const newDetails = newRows.map((item) => ({
							...item,
							boxList: [],
							boxLoadStatus: "loadmore",
							boxPageNum: 1,
						}));

						this.detailList = [...this.detailList, ...newDetails];
						this.detailLoadStatus =
							newRows.length < this.queryParams.pageSize ? "nomore" : "loadmore";
					}
				} catch (error) {
					console.error("加载更多失败:", error);
					this.detailLoadStatus = "loadmore";
				}
			},

			// 获取箱标签列表
			async getBoxList(detailIndex) {
				const detailItem = this.detailList[detailIndex];
				if (!detailItem || detailItem.boxLoadStatus === "nomore") return;

				try {
					detailItem.boxLoadStatus = "loading";
					const params = {
						pageNum: detailItem.boxPageNum,
						pageSize: 5,
						receiveDetailId: detailItem.id,
					};

					const res = await receiveBox(params);
					if (res.code === 200) {
						const newBoxes = res.rows || [];
						detailItem.boxList = [...detailItem.boxList, ...newBoxes];
						detailItem.boxPageNum += 1;
						detailItem.boxLoadStatus =
							newBoxes.length < params.pageSize ? "nomore" : "loadmore";
					}
				} catch (error) {
					console.error("获取箱标签失败:", error);
					detailItem.boxLoadStatus = "loadmore";
				}
			},

			// 切换箱标签显示
			toggleBoxList(index) {
				// 创建新数组避免直接修改
				const newExpanded = [...this.expandedItems];

				// 切换当前项的展开状态
				if (newExpanded.includes(index)) {
					// 如果已展开则移除
					newExpanded.splice(newExpanded.indexOf(index), 1);
				} else {
					// 如果未展开则添加
					newExpanded.push(index);
				}

				this.expandedItems = newExpanded;

				// 首次展开时加载箱标签（保持原逻辑）
				if (this.expandedItems.includes(index) &&
					this.detailList[index].boxList.length === 0) {
					this.getPurDetaiList();
				}
			},

			// 扫码
			async handleScan() {
				try {
					const res = await uni.scanCode();
					this.searchValue = res.result;
					await this.handleSearch(res.result);
				} catch (error) {
					// console.error('扫码失败:', error)
					this.$u.toast(error.msg);
				}
			},

			// 搜索箱标签
			async handleSearch(value) {
				if (!value) return;

				try {
					uni.showLoading({
						title: "处理中...",
					});

					const params = {
						id: this.objData.purchaseNo,
						boxNo: value,
					};

					const result = await scanCode(params);
				
					if (result.code === 200) {
						this.$u.toast(result.msg || "操作成功");
						
						await this.getPurDetaiList();
					} else {
						this.$u.toast(result.msg || "操作失败");
					}
				} catch (error) {
					console.error('搜索失败:', error)
					this.$u.toast(error.msg);
				} finally {
					this.searchValue = null
					uni.hideLoading();
				}
				uni.hideLoading();
			},

			// 删除箱标签
			async handleDeleteBox(boxId, detailIndex, boxIndex) {
			
				try {
					uni.showLoading({
						title: "删除中..."
					});
					const res = await deleteBox(boxId);

					if (res.code === 200) {
						this.$u.toast("删除成功");
						this.detailList[detailIndex].boxList.splice(boxIndex, 1);
					} else {
						this.$u.toast(res.msg || "删除失败");
					}
				} catch (error) {
					console.error("删除失败:", error);
					this.$u.toast("删除失败");
				} finally {
					uni.hideLoading();
				}
			},

			// 提交
			async handleSubmit() {
				try {

					if (this.count == 0) {
						return this.$u.toast('请先扫描箱标签再进行提交')
					}
					
					this.submitting = true;
					const res = await submitPurchase(this.objData.purchaseNo);

					if (res.code === 200) {
						this.$u.toast("提交成功");
						// 触发自定义事件通知 index 页面刷新列表
						 uni.$emit('refreshPurchaseList');
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
						this.getPurDetaiList(); //更新采购单明细
					} else {
						this.$u.toast(res.msg || "提交失败");
					}
				} catch (error) {
					console.error("提交失败信息:", error);
					this.$u.toast("提交失败");
				} finally {
					this.submitting = false;
				}
			},

			// 折叠面板变化
			handleCollapseOpen(name) {
				if(name === 'detail'){
					this.listKey+=1;
				}
			},
			// 格式化时间
			formatTime(time) {
				return time ? formatDate(time, "YYYY-MM-DD HH:mm") : "";
			},

			// 获取状态文本
			getLineStateText(state) {
				const stateItem = this.lineState.find((item) => item.dictValue === state);
				return stateItem ? stateItem.dictLabel : "未知状态";
			},
		},
	};
</script>

<style lang="scss" scoped>
	::v-deep .u-cell__title-text.data-v-1c4434ae {
		font-size: 15px;
		line-height: 22px;
		color: #303133;
		font-weight: 600;
	}

	.purchase-detail-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
		position: relative;
		padding-bottom: 300rpx;

		.search-box {
			padding: 16rpx;
			background-color: #fff;
		}

		.u-collapse {
			flex: 1;
			overflow: hidden;
			background-color: #fff;

			.purchase-info {
				background-color: #fff;
				padding: 20rpx;

				.info-item {
					margin-bottom: 16rpx;
					font-size: 28rpx;

					.label {
						color: #666;
					}

					.value {
						color: #333;
					}
				}
			}

			/* 扫描统计 */
			.scan-summary {
				display: flex;
				justify-content: flex-end;
				align-items: center;
				padding: 20rpx 0;
				font-size: 28rpx;
				background-color: #fff;
				border-radius: 16rpx;
				margin-bottom: 20rpx;
				margin-top: 20rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

				.label {
					color: #666;
					font-weight: 500;
				}

				.value {
					color: #2979ff;
					font-weight: bold;
					margin-left: 15rpx;
					margin-right: 15rpx;
					font-size: 32rpx;
				}
			}

			.detail-list {
				// height: 100%;
				height: auto !important;
				.detail-card {
					margin: 20rpx;
					border-radius: 12rpx;
					background-color: #fff;
					box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

					.card-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 20rpx;
						border-bottom: 1rpx solid #f0f0f0;

						.title {
							font-size: 30rpx;
							font-weight: bold;
							color: #333;
						}

						.status {
							padding: 4rpx 13rpx;
							background-color: #00e813;
							border-radius: 10rpx;
							font-size: 24rpx;
							color: #584c4c;
						}

						.createdState {
							padding: 4rpx 13rpx;
							background-color: #2e8be8;
							border-radius: 10rpx;
							font-size: 24rpx;
							color: #ffffff;
						}

					}

					.card-content {
						padding: 20rpx;

						.content-row {
							margin-bottom: 16rpx;
							font-size: 28rpx;

							.label {
								color: #666;
							}

							.value {
								color: #333;
							}

							&.double,
							&.triple {
								display: flex;

								.col {
									flex: 1;
								}
							}
						}
					}

					.box-tags {
						border-top: 1rpx solid #f0f0f0;

						.box-header {
							display: flex;
							justify-content: space-between;
							align-items: center;
							padding: 20rpx;

							.title {
								font-size: 28rpx;
								font-weight: bold;
								color: #333;
							}
						}

						.box-list {
							padding: 0 20rpx 20rpx;

							.box-item {
								position: relative;
								padding: 16rpx;
								margin-bottom: 16rpx;
								border: 1rpx solid #e6f7ff;
								border-radius: 8rpx;
								background-color: #f6fbff;

								.box-info {
									margin-bottom: 8rpx;
									font-size: 26rpx;

									.label {
										color: #666;
									}

									.value {
										color: #333;
									}
								}

								.box-action {
									position: absolute;
									right: 12rpx;
									bottom: 76rpx;

								}
							}
						}
					}
				}
			}
		}

		.action-buttons {
			padding: 20rpx 0;
			background-color: #fff;
			border-top: 1rpx solid #f0f0f0;
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;

			::v-deep .u-button {
				height: 80rpx;
				font-size: 32rpx;
				margin: 0;
				width: 80% !important;
			}
		}
	}
</style>