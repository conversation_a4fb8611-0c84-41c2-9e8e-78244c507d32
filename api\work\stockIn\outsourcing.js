import request from '@/config/request.js';

// 获取委外入库列表
//export const outsouList = (params) => request.get('/system/wms_outsourcing_return/list', params)

export const outsouList = (params) => request.get('/system/stock_in/listByInspected', params)

// 获取采购入库列表详情
export const stockInDetail = (id) => request.get('/system/stock_in/' + id)

// 获取采购入库库位查询
export const stockCode = (data) => request.post('/system/stock_in/binCode' , data)

// 采购入库根据主表id查询列表及暂存
export const detailList = (id) => request.get('/system/stock_in/detail/list' , id)

// 采购入库扫箱号
export const stockInBo = (data) => request.post('/system/stock_in/getStockInBox' , data)

// 采购入库提交
export const stockInSubmit = (data) => request.post('/system/stock_in/submit' , data)

// 删除入库单箱信息
export const delStockBox = (id) => request.delete('/system/stock_box/' + id)