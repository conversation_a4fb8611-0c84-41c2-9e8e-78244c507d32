<template>
	<view class="procurement-detail">
		<!-- 固定搜索栏 -->
		<u-sticky>
			<view class="search-header">
				<!-- 库位搜索 -->
				<view class="search-item">
					<u-search v-model="queryParams.locationCode" placeholder="请输入或扫描库位编号" :showAction="false"
						actionText="搜索" shape="square" :disabled="disable" searchIcon="scan" @clickIcon="scanLocation"
						@search="locationSearch" @custom="locationSearch">
						<template #label>
							<view class="search-label">库位</view>
						</template>
					</u-search>
				</view>

				<!-- 栈板/箱号搜索 -->
				<view class="search-item">
					<u-search v-model="queryParams.containerNo" placeholder="请输入或扫描栈板/箱号" :showAction="false"
						actionText="搜索" shape="square" searchIcon="scan" @clickIcon="scanContainer"
						@search="containerSearch" @custom="containerSearch">
						<template #label>
							<view class="search-label">栈板/箱号</view>
						</template>
					</u-search>
				</view>
			</view>
		</u-sticky>
		<!-- 主内容区 -->
		<view class="content-container">
			<!-- 单据信息卡片 -->
			<view class="document-card" v-if="Object.keys(locationData).length !== 0">
				<view class="document-row">
					<text class="label">实扫库位：{{ locationData.warehouseCode }} -{{ locationData.areaCode }} - {{ locationData.locationCode }}</text>
				</view>
			</view>
			<!-- 折叠面板 -->
			<u-collapse :value="collapseActive" @change="handleCollapseChange">
				<!-- 采购单明细 -->
				<u-collapse-item name="detail" :title="`箱明细 (${detailList.length})`" v-if="detailList.length > 0">
					<view class="detail-list">
						<u-list v-if="detailList.length > 0" height="calc(100vh - 400rpx)">
							<u-list-item v-for="(item, index) in detailList" :key="item.id">
								<view class="detail-card">
									<view class="card-header">
										<text class="title">序号 {{ index + 1 }}</text>
									</view>

									<view class="card-content">
										<view class="content-row">
											<text class="label">箱标签：</text>
											<text class="value">{{ item.boxNo || "--" }}</text>
										</view>
										<view class="content-row">
											<text class="label">采购订单：</text>
											<text class="value">{{ item.purchaseNo || "--" }}</text>
										</view>
										<view class="content-row">
											<text class="label">物料编号：</text>
											<text class="value">{{ item.materialCode || "--" }}</text>
										</view>
										<view class="content-row">
											<text class="label">物料名称：</text>
											<text class="value">{{ item.materialName || "--" }}</text>
										</view>
										<view class="content-row">
											<text class="label">规格型号：</text>
											<text class="value">{{ item.specification || "--" }}</text>
										</view>
										<view class="content-row double">
											<view class="col">
												<text class="label">批次：</text>
												<text class="value">{{ item.batchNo || "--" }}</text>
											</view>
											<view class="col">
												<text class="label">单位：</text>
												<text class="value">{{ item.materialUnit || "--" }}</text>
											</view>
										</view>
										<view class="content-row triple">
											<view class="col">
												<text class="label">数量：</text>
												<text class="value">{{ item.qty || 0 }}</text>
											</view>
											<view class="box-action">
												<u-button type="error" size="mini"
													@click="handleDeleteBox(item.id, index)">删除</u-button>
											</view>
										</view>
									</view>
								</view>
							</u-list-item>

							<u-loadmore :status="detailLoadStatus" />
						</u-list>

						<u-empty v-else mode="list" />
					</view>
				</u-collapse-item>
			</u-collapse>
		</view>
		<!-- 底部操作按钮 -->
		<view class="action-buttons">
			<u-button type="primary" throttleTime="1000" @click="handleSubmit" :loading="submitting" :disabled="detailList.length === 0">提交</u-button>
		</view>
	</view>
</template>

<script>
	import * as procureApi from "@/api/work/stockIn/procurement.js";
	import * as boxApi from "@/api/work/box/box.js";
	export default {
		data() {
			return {
				queryParams: {
					locationCode: "", // 库位编号
					containerNo: "", // 栈板/箱号
					stockInNo: "", // 入库单号
				},
				locationData: {}, //库位数据
				disable: false, //库位是否可输入
				objData: {}, //页面传参
				detailList: [],
				baseBoxInfo: null, // 基准箱信息（用于比较物料、批次等）
				collapseActive: ["detail"], // 默认展开所有折叠项
				submitting: false,
				detailLoadStatus: 'loadmore'
			};
		},
		async onLoad(options) {
			if (options?.params) {
				const item = JSON.parse(decodeURIComponent(options.params));
				this.objData = item;
			}
		},
		methods: {
			// 折叠面板变化
			handleCollapseChange(value) {
				this.collapseActive = value;
			},
			// 扫描库位
			scanLocation() {
				this.scanCode("locationCode", "库位");
			},

			// 扫描箱号
			scanContainer() {
				this.scanCode("containerNo", "箱号");
			},

			// 通用扫码方法
			scanCode(field, type) {
				uni.scanCode({
					success: (res) => {
						this.queryParams[field] = res.result;
						this.$u.toast(`${type}扫码成功`);
						
						// 扫码后自动触发搜索
						if (field === 'locationCode') {
							this.locationSearch();
						} else if (field === 'containerNo') {
							this.containerSearch();
						}
					},
					fail: () => {
						this.$u.toast(`${type}扫码失败，请重试`);
					},
				});
			},
			// 库位搜索
			async locationSearch() {
				if (!this.queryParams.locationCode.trim()) {
					this.$u.toast("请输入库位编号");
					return;
				}
				await this.handleSearch("location");
			},
			// 箱号搜索
			async containerSearch() {
				if (!this.queryParams.locationCode.trim()) {
					this.$u.toast("请先输入库位编号");
					return;
				}
				if (!this.queryParams.containerNo.trim()) {
					this.$u.toast("请输入栈板/箱号");
					return;
				}

				await this.handleSearch("container");
			},
			// 通用搜索方法
			async handleSearch(type) {
				try {
					this.loading = true;
					
					if (type === "location") {
						const res = await procureApi.stockCode({
							locationCode: this.queryParams.locationCode,
						});
						
						if (res.code === 200) {
							this.locationData = res.data;
							this.disable = true;
							this.$u.toast("库位已锁定！");
							// 清空之前的箱明细
							this.detailList = [];
							this.baseBoxInfo = null;
						} else {
							this.$u.toast(res.msg || "库位查询失败");
						}
					} else if (type === "container") {
						if (!this.locationData || Object.keys(this.locationData).length === 0) {
							this.$u.toast("请先扫描有效的库位");
							return;
						}
						
						const params = {
							qrCode: this.queryParams.containerNo,
						};
						const result = await boxApi.listBox(params);
						
						if (result.code !== 200) {
							this.$u.toast(result.msg || "箱号查询失败");
							return;
						}
						
						if (!result.rows || result.rows.length === 0) {
							this.$u.toast("未找到对应的箱信息");
							return;
						}
						
						const boxInfo = result.rows[0];
						console.log(boxInfo,'boxInfo');
						// 检查箱状态
						if (boxInfo.boxState !== "STOCK_IN") {
							this.$u.toast("请使用已入库的箱号");
							return;
						}
						
						// 检查是否重复添加
						if (this.detailList.some(item => item.boxNo === boxInfo.boxNo)) {
							this.$u.toast("不可重复添加箱号");
							return;
						}
						
						// 设置或检查基准箱信息
						if (this.detailList.length === 0) {
							// 第一个箱，设为基准
							this.baseBoxInfo = {
								supplierCode: boxInfo.supplierCode,
								partCode: boxInfo.partCode,
								batchNo: boxInfo.batchNo
							};
							this.detailList.push(boxInfo);
							this.$u.toast("箱号添加成功");
						} else {
							// 检查物料、供应商、批次是否一致
							if (boxInfo.supplierCode === this.baseBoxInfo.supplierCode &&
								boxInfo.partCode === this.baseBoxInfo.partCode &&
								boxInfo.batchNo === this.baseBoxInfo.batchNo) {
								this.detailList.push(boxInfo);
								this.$u.toast("箱号添加成功");
							} else {
								this.$u.toast("标签的物料、供应商、批次必须相同");
								return;
							}
						}
						
						// 清空输入框
						this.queryParams.containerNo = "";
					}
				} catch (error) {
					console.error("操作失败:", error);
					this.$u.toast(error.message || "操作失败");
				} finally {
					this.loading = false;
				}
			},
			
			// 删除箱标签
			handleDeleteBox(id, index) {
				this.detailList.splice(index, 1);
				this.$u.toast('删除成功');
				
				// 如果删除后列表为空，重置基准信息
				if (this.detailList.length === 0) {
					this.baseBoxInfo = null;
				}
			},
			
			// 提交
			async handleSubmit() {
				if (this.detailList.length === 0) {
					this.$u.toast("请至少添加一个箱号");
					return;
				}
				
				try {
					this.submitting = true;
					const res = await boxApi.recombinationSubmit(this.detailList);

					if (res.code === 200) {
						this.$u.toast("提交成功");
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						this.$u.toast(res.msg || "提交失败");
					}
				} catch (error) {
					console.error("提交失败:", error);
					this.$u.toast(error.msg || "提交异常");
				} finally {
					this.submitting = false;
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	.procurement-detail {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 100rpx;
	}

	/* 搜索栏样式 */
	.search-header {
		padding: 20rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	}

	.search-item {
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.search-label {
		width: 140rpx;
		color: #333;
		text-align: center;
	}

	/* 内容区样式 */
	.content-container {
		padding: 20rpx;
	}

	/* 单据信息卡片 */
	.document-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.document-row {
		margin-bottom: 16rpx;
		font-size: 28rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;

		.label {
			font-weight: 500;
			color: #333;
		}
	}

	/* 物料卡片 */
	.detail-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
		padding-bottom: 16rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.title {
			font-size: 30rpx;
			font-weight: bold;
			color: #333;
		}
	}

	.card-content {
		.content-row {
			display: flex;
			margin-bottom: 16rpx;
			font-size: 28rpx;

			.label {
				color: #666;
				width: 140rpx;
				flex-shrink: 0;
			}

			.value {
				color: #333;
				flex: 1;
			}

			&.double, &.triple {
				display: flex;
				justify-content: space-between;

				.col {
					flex: 1;
					display: flex;
				}
			}

			&.triple {
				.box-action {
					margin-left: 20rpx;
					display: flex;
					align-items: center;
				}
			}
		}
	}

	.action-buttons {
		padding: 20rpx;
		background-color: #fff;
		border-top: 1rpx solid #f0f0f0;
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;

		::v-deep .u-button {
			height: 80rpx;
			font-size: 32rpx;
			width: 90% !important;
		}
	}

	/* 折叠面板标题样式 */
	::v-deep .u-collapse-item__title__text {
		font-weight: bold;
		font-size: 30rpx;
	}
</style>