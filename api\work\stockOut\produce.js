import { data } from '../../../uni_modules/uview-ui/libs/mixin/mixin';
import request from '@/config/request.js';

// 获取出库列表
export const produceList = (params) => request.get('/system/stockOut/list', params)

// 获取出库详情
// export const produceDetail = (params) => request.get('/system/stockOutBox/scanCodet', params)


// 获取出库详情
export const stockOutDetail = (id) => request.get('/system/stockOut/getDetails/' + id)


// 获取出库详情和暂存的箱
export const getDetailsAndBox = (id) => request.get('/system/stockOut/getDetailsAndBox/' + id)

// 出库扫箱
export const stockOutBox = (data) => request.post('/system/stockOutBox/scanCode' , data)

// 采购退货提交
export const stockOutSubmit = (data) => request.post('/system/stockOut/submitReturnApp',data)

// 生产退货提交
export const stockOutSubmitP = (data) => request.post('/system/stockOut/submitApp',data)

// 销售发货
export const submitSalesApp = (data) => request.post('/system/stockOut/submitSalesApp',data)

// 删除生产出库箱信息
export const delStockBox = (id) => request.delete('/system/stockOutBox/' + id)
