<script>
export default {
  onLaunch() {
    if (uni.getStorageSync('AccessToken')) {
      uni.reLaunch({
        url: "/pages/work/index",
        success: () => {
          // #ifdef APP-PLUS
		
          if (plus) {
            plus.navigator.closeSplashscreen();
          }
          // #endif
        },
      });
    } else {
      uni.reLaunch({
        url: "/pages/login/index",
        success: () => {
          // #ifdef APP-PLUS
          if (plus) {
            plus.navigator.closeSplashscreen();
          }
          // #endif
        },
      });
    }
    // 加载系统信息
    this.$store.dispatch('SystemInfo');
  },
  onShow() {},
  onHide() {}
};
</script>

<style lang="scss">
@import "@/uni_modules/uview-ui/index.scss";
@import "@/static/style.scss";
@import "@/static/iconfon/iconfont.css";
</style>