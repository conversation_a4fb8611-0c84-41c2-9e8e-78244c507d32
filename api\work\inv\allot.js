import request from '@/config/request.js';

// 获取调拨入库列表(√)
export const listAllot = (params) => request.get('/system/allot/list', params)


// 获取调拨入库列表详情(√)
export const allotDetail = (id) => request.get('/system/allotDetail/' + id)

// 获取调拨入库库位查询(√)
export const allotCode = (data) => request.post('/system/allotBox/binCode' , data)

// 调拨入库根据主表id查询列表及暂存()
export const detailList = (id) => request.get('/system/allot/detail/list' , id)

// 调拨入库扫箱号(√)
export const allotBo = (data) => request.post('/system/allotBox/scanCode' , data)

// 调拨入库提交(√)
export const allotSubmit = (data) => request.post('/system/allot/submitAppAllot' , data)

// 删除入库单箱信息
export const delAllotBox = (id) => request.delete('/system/allotBox/' + id)